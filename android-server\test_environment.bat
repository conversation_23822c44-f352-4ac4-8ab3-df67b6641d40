@echo off
chcp 65001 >nul
title 环境检查和脚本测试

echo.
echo ==========================================
echo          环境检查和脚本测试
echo ==========================================
echo.

set PASS_COUNT=0
set FAIL_COUNT=0

:: 1. 检查Java环境
echo [检查 1/7] Java环境...
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Java 环境正常
    set /a PASS_COUNT+=1
) else (
    echo ✗ Java 环境未配置
    set /a FAIL_COUNT+=1
)

:: 2. 检查Android SDK
echo [检查 2/7] Android SDK...
if defined ANDROID_SDK_ROOT (
    echo ✓ ANDROID_SDK_ROOT: %ANDROID_SDK_ROOT%
    set /a PASS_COUNT+=1
) else if defined ANDROID_HOME (
    echo ✓ ANDROID_HOME: %ANDROID_HOME%
    set /a PASS_COUNT+=1
) else (
    echo ✗ Android SDK 环境变量未设置
    set /a FAIL_COUNT+=1
)

:: 3. 检查ADB
echo [检查 3/7] ADB工具...
where adb >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ ADB 工具可用
    adb version | findstr "Android Debug Bridge"
    set /a PASS_COUNT+=1
) else (
    echo ✗ ADB 工具未找到
    set /a FAIL_COUNT+=1
)

:: 4. 检查AAPT
echo [检查 4/7] AAPT工具...
where aapt >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ AAPT 工具可用
    set /a PASS_COUNT+=1
) else (
    echo ✗ AAPT 工具未找到
    set /a FAIL_COUNT+=1
)

:: 5. 检查Gradle Wrapper
echo [检查 5/7] Gradle Wrapper...
if exist gradlew.bat (
    echo ✓ Gradle Wrapper 存在
    set /a PASS_COUNT+=1
) else (
    echo ✗ Gradle Wrapper 不存在
    set /a FAIL_COUNT+=1
)

:: 6. 检查Android设备
echo [检查 6/7] Android设备连接...
if %PASS_COUNT% geq 3 (
    adb devices > temp_devices.txt 2>&1
    findstr "device$" temp_devices.txt >nul
    if %errorlevel% equ 0 (
        echo ✓ 检测到已连接的Android设备:
        findstr "device$" temp_devices.txt
        set /a PASS_COUNT+=1
    ) else (
        echo ⚠ 未检测到已连接的Android设备
        echo   注意: 这不影响编译，但会影响安装测试
        set /a PASS_COUNT+=1
    )
    del temp_devices.txt >nul 2>&1
) else (
    echo ⚠ 无法检查设备 (需要ADB工具)
)

:: 7. 检查项目文件
echo [检查 7/7] 项目文件完整性...
set PROJECT_FILES_OK=1

if not exist "app\build.gradle" (
    echo ✗ 缺少 app\build.gradle
    set PROJECT_FILES_OK=0
)

if not exist "app\src\main\AndroidManifest.xml" (
    echo ✗ 缺少 AndroidManifest.xml
    set PROJECT_FILES_OK=0
)

if not exist "app\src\main\java\com\remotecontrol\android\MainActivity.kt" (
    echo ✗ 缺少 MainActivity.kt
    set PROJECT_FILES_OK=0
)

if %PROJECT_FILES_OK% equ 1 (
    echo ✓ 项目文件完整
    set /a PASS_COUNT+=1
) else (
    echo ✗ 项目文件不完整
    set /a FAIL_COUNT+=1
)

echo.
echo ==========================================
echo 检查结果: %PASS_COUNT% 通过, %FAIL_COUNT% 失败
echo ==========================================

if %FAIL_COUNT% equ 0 (
    echo.
    echo ✓ 所有检查通过! 环境配置正确
    echo.
    echo 建议的下一步操作:
    echo 1. 运行 build_apk.bat 编译APK
    echo 2. 运行 configure_apk.bat 配置和安装APK
    echo 3. 或运行 Configure-APK.ps1 使用PowerShell脚本
) else if %PASS_COUNT% geq 4 (
    echo.
    echo ⚠ 基本环境可用，但有些问题需要解决
    echo.
    echo 可能的解决方案:
    if %FAIL_COUNT% gtr 0 echo - 检查Android SDK安装和环境变量配置
    echo - 确保PATH包含Android SDK的platform-tools和build-tools目录
    echo - 连接Android设备并启用USB调试
) else (
    echo.
    echo ✗ 环境配置有严重问题，需要先解决以下问题:
    echo.
    if %JAVA_ERROR% equ 1 echo - 安装Java JDK 8或更高版本
    echo - 安装并配置Android SDK
    echo - 设置ANDROID_SDK_ROOT或ANDROID_HOME环境变量
    echo - 将Android SDK工具目录添加到PATH
)

echo.
echo 详细环境信息:
echo PATH中的相关路径:
echo %PATH% | findstr /I "android"
echo.

set /p TEST_SCRIPTS="是否测试构建和配置脚本? (需要完整环境) (y/N): "
if /i "%TEST_SCRIPTS%"=="y" (
    echo.
    echo 开始脚本测试...
    echo.
    
    echo [测试] 构建脚本语法检查...
    if exist build_apk.bat (
        echo ✓ build_apk.bat 存在
    ) else (
        echo ✗ build_apk.bat 不存在
    )
    
    echo [测试] 配置脚本语法检查...
    if exist configure_apk.bat (
        echo ✓ configure_apk.bat 存在
    ) else (
        echo ✗ configure_apk.bat 不存在
    )
    
    if exist Configure-APK.ps1 (
        echo ✓ Configure-APK.ps1 存在
        echo [测试] PowerShell脚本语法...
        powershell -Command "Get-Content 'Configure-APK.ps1' | ForEach-Object { if ($_ -match '^\s*#.*') { } }" >nul 2>&1
        if %errorlevel% equ 0 (
            echo ✓ PowerShell脚本语法正常
        ) else (
            echo ⚠ PowerShell脚本可能有语法问题
        )
    ) else (
        echo ✗ Configure-APK.ps1 不存在
    )
)

echo.
echo 按任意键退出...
pause >nul