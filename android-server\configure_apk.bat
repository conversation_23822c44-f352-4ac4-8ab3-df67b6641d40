@echo off
chcp 65001 >nul
title Android远程控制APK配置工具

echo.
echo ==========================================
echo    Android远程控制APK配置工具
echo ==========================================
echo.

:: 检查是否安装了必要工具
where aapt >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到 aapt 工具，请确保已安装Android SDK
    echo 请将Android SDK的build-tools目录添加到PATH环境变量
    pause
    exit /b 1
)

where adb >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到 adb 工具，请确保已安装Android SDK
    echo 请将Android SDK的platform-tools目录添加到PATH环境变量
    pause
    exit /b 1
)

:: 设置变量
set APK_FILE=%~dp0app\build\outputs\apk\debug\app-debug.apk
set TEMP_DIR=%TEMP%\android_remote_config
set PACKAGE_NAME=com.remotecontrol.android

:: 检查APK文件是否存在
if not exist "%APK_FILE%" (
    echo [错误] 未找到APK文件: %APK_FILE%
    echo 请先编译Android项目生成APK文件
    pause
    exit /b 1
)

echo [信息] 找到APK文件: %APK_FILE%
echo.

:: 获取用户输入
echo 请配置连接参数:
echo.

:input_ip
set /p PC_IP="请输入PC端IP地址 (默认: *************): "
if "%PC_IP%"=="" set PC_IP=*************
echo 验证IP地址格式...

:: 简单IP格式验证
echo %PC_IP% | findstr /R "^[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*$" >nul
if %errorlevel% neq 0 (
    echo [错误] IP地址格式无效，请重新输入
    goto input_ip
)

:input_port
set /p PC_PORT="请输入PC端端口 (默认: 8888): "
if "%PC_PORT%"=="" set PC_PORT=8888

:: 验证端口范围
if %PC_PORT% lss 1 (
    echo [错误] 端口号必须大于0，请重新输入
    goto input_port
)
if %PC_PORT% gtr 65535 (
    echo [错误] 端口号必须小于65536，请重新输入
    goto input_port
)

set /p AUTO_START="是否启用自动连接? (y/N): "
if /i "%AUTO_START%"=="y" (
    set AUTO_START_VALUE=true
) else (
    set AUTO_START_VALUE=false
)

echo.
echo ==========================================
echo 配置信息确认:
echo PC IP地址: %PC_IP%
echo PC端口:    %PC_PORT%
echo 自动连接:  %AUTO_START_VALUE%
echo ==========================================
echo.

set /p CONFIRM="确认配置并安装APK? (Y/n): "
if /i "%CONFIRM%"=="n" (
    echo 已取消配置
    pause
    exit /b 0
)

echo.
echo [信息] 开始配置APK...

:: 创建临时目录
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"
mkdir "%TEMP_DIR%"

:: 检查是否有连接的设备
echo [信息] 检查Android设备连接...
adb devices | findstr /R "device$" >nul
if %errorlevel% neq 0 (
    echo [警告] 未检测到连接的Android设备
    echo 请确保:
    echo 1. Android设备已连接到电脑
    echo 2. 已启用USB调试
    echo 3. 已允许此电脑的USB调试授权
    echo.
    set /p SKIP_INSTALL="是否跳过自动安装? (Y/n): "
    if /i not "%SKIP_INSTALL%"=="n" (
        goto create_config_only
    )
)

:: 安装APK
echo [信息] 正在安装APK到设备...
adb install -r "%APK_FILE%"
if %errorlevel% neq 0 (
    echo [错误] APK安装失败
    pause
    exit /b 1
)

echo [信息] APK安装成功

:: 配置应用设置
echo [信息] 正在配置应用设置...

:: 启动应用并设置参数 (需要root权限或特殊权限)
adb shell am start -n %PACKAGE_NAME%/.MainActivity
timeout /t 2 >nul

:: 如果有root权限，可以直接修改SharedPreferences文件
echo [信息] 尝试配置连接参数...
adb shell "echo 'PC_IP=%PC_IP%' > /sdcard/remote_control_config.txt"
adb shell "echo 'PC_PORT=%PC_PORT%' >> /sdcard/remote_control_config.txt"
adb shell "echo 'AUTO_START=%AUTO_START_VALUE%' >> /sdcard/remote_control_config.txt"

echo [信息] 配置文件已创建在设备的 /sdcard/remote_control_config.txt

goto finish

:create_config_only
echo [信息] 创建配置文件...
echo PC_IP=%PC_IP% > "%~dp0remote_control_config.txt"
echo PC_PORT=%PC_PORT% >> "%~dp0remote_control_config.txt"
echo AUTO_START=%AUTO_START_VALUE% >> "%~dp0remote_control_config.txt"

echo [信息] 配置文件已创建: %~dp0remote_control_config.txt
echo 请手动安装APK文件: %APK_FILE%

:finish
echo.
echo ==========================================
echo 配置完成!
echo ==========================================
echo.
echo 使用说明:
echo 1. 在Android设备上打开"Android远程控制"应用
echo 2. 授予所需权限 (屏幕录制、无障碍服务、悬浮窗)
echo 3. 确认PC端IP和端口设置正确
echo 4. 点击"连接"按钮连接到PC
echo.
echo 注意事项:
echo - 确保Android设备和PC在同一网络中
echo - PC端需要先启动服务并显示正确的IP地址
echo - 如果连接失败，请检查防火墙设置
echo.

:: 清理临时文件
if exist "%TEMP_DIR%" rmdir /s /q "%TEMP_DIR%"

echo 按任意键退出...
pause >nul