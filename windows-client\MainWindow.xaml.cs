using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using System.Windows.Threading;
using System.Threading.Tasks;
using AndroidRemoteClient.Core;
using AndroidRemoteClient.Models;

namespace AndroidRemoteClient
{
    public partial class MainWindow : Window
    {
        private AndroidRemoteServer _server;
        private DispatcherTimer _refreshTimer;
        private bool _isLeftMousePressed = false;
        private bool _isRightMousePressed = false;
        private Point _lastMousePosition;
        private DateTime _lastFrameTime = DateTime.Now;
        private int _frameCount = 0;

        public MainWindow()
        {
            InitializeComponent();
            InitializeServer();
            InitializeTimer();
            InitializeEventHandlers();
            LoadNetworkInterfaces();
        }

        private void InitializeServer()
        {
            _server = new AndroidRemoteServer();
            _server.ServerStatusChanged += OnServerStatusChanged;
            _server.ClientConnectionChanged += OnClientConnectionChanged;
            _server.DeviceInfoReceived += OnDeviceInfoReceived;
            _server.ScreenDataReceived += OnScreenDataReceived;
            _server.ErrorOccurred += OnErrorOccurred;
            _server.StatusMessageReceived += OnStatusMessageReceived;
        }

        private void InitializeTimer()
        {
            _refreshTimer = new DispatcherTimer();
            _refreshTimer.Interval = TimeSpan.FromSeconds(1);
            _refreshTimer.Tick += RefreshTimer_Tick;
        }

        private void InitializeEventHandlers()
        {
            chkAutoRefresh.Checked += (s, e) => { /* 自动刷新由Android端控制 */ };
            chkAutoRefresh.Unchecked += (s, e) => { /* 自动刷新由Android端控制 */ };
        }

        private void LoadNetworkInterfaces()
        {
            try
            {
                var availableIPs = _server.GetAvailableIPAddresses();
                cmbListenIP.Items.Clear();
                
                // 添加特殊选项
                cmbListenIP.Items.Add("0.0.0.0 (所有网卡)");
                cmbListenIP.Items.Add("127.0.0.1 (本地回环)");
                
                // 添加实际网络接口IP
                foreach (var ip in availableIPs)
                {
                    if (ip != "0.0.0.0" && ip != "127.0.0.1")
                    {
                        cmbListenIP.Items.Add($"{ip} (网络接口)");
                    }
                }
                
                cmbListenIP.SelectedIndex = 0; // 默认选择监听所有接口
            }
            catch (Exception ex)
            {
                txtStatus.Text = $"加载网络接口失败: {ex.Message}";
            }
        }

        #region 服务器管理
        private async void BtnStartServer_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                btnStartServer.IsEnabled = false;
                cmbListenIP.IsEnabled = false;
                txtPort.IsEnabled = false;
                txtStatus.Text = "正在启动服务器...";

                // 解析选择的监听IP
                var selectedItem = cmbListenIP.Text;
                string listenIP;
                
                if (selectedItem.StartsWith("0.0.0.0"))
                {
                    listenIP = "0.0.0.0";
                }
                else if (selectedItem.StartsWith("127.0.0.1"))
                {
                    listenIP = "127.0.0.1";
                }
                else
                {
                    // 提取IP地址（格式: "************* (网络接口)"）
                    var spaceIndex = selectedItem.IndexOf(' ');
                    listenIP = spaceIndex > 0 ? selectedItem.Substring(0, spaceIndex) : selectedItem;
                }

                var port = int.Parse(txtPort.Text);
                var success = await _server.StartServerAsync(listenIP, port);
                
                if (success)
                {
                    txtStatus.Text = "服务器启动成功，等待设备连接...";
                    lblServerIP.Content = _server.ActualServerIP;
                }
                else
                {
                    txtStatus.Text = "服务器启动失败";
                    EnableServerControls(true);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"启动服务器错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                EnableServerControls(true);
                txtStatus.Text = "启动失败";
            }
        }

        private async void BtnStopServer_Click(object sender, RoutedEventArgs e)
        {
            await _server.StopServerAsync();
            EnableControlButtons(false);
            EnableServerControls(true);
            ClearDeviceInfo();
            screenImage.Source = null;
            txtStatus.Text = "服务器已停止";
            lblServerIP.Content = "未启动";
        }

        private void OnServerStatusChanged(bool isRunning)
        {
            Dispatcher.Invoke(() =>
            {
                btnStartServer.IsEnabled = !isRunning;
                btnStopServer.IsEnabled = isRunning;
                EnableServerControls(!isRunning);
            });
        }

        private void EnableServerControls(bool enabled)
        {
            cmbListenIP.IsEnabled = enabled;
            txtPort.IsEnabled = enabled;
            if (enabled)
            {
                btnStartServer.IsEnabled = true;
                btnStopServer.IsEnabled = false;
            }
        }

        private void OnClientConnectionChanged(bool isConnected)
        {
            Dispatcher.Invoke(() =>
            {
                lblConnectionStatus.Content = isConnected ? "设备已连接" : "无设备连接";
                EnableControlButtons(isConnected);

                if (!isConnected)
                {
                    screenImage.Source = null;
                    ClearDeviceInfo();
                }
            });
        }
        #endregion

        #region 设备信息
        private void OnDeviceInfoReceived(DeviceInfo deviceInfo)
        {
            Dispatcher.Invoke(() =>
            {
                lblDeviceModel.Text = $"型号: {deviceInfo.Model}";
                lblAndroidVersion.Text = $"Android版本: {deviceInfo.AndroidVersion}";
                lblScreenResolution.Text = $"屏幕分辨率: {deviceInfo.ScreenWidth}x{deviceInfo.ScreenHeight}";

                // 设置画布大小为设备的实际分辨率，Viewbox会自动缩放
                screenCanvas.Width = deviceInfo.ScreenWidth;
                screenCanvas.Height = deviceInfo.ScreenHeight;
            });
        }

        private void ClearDeviceInfo()
        {
            lblDeviceModel.Text = "型号: 无设备";
            lblAndroidVersion.Text = "Android版本: 无设备";
            lblScreenResolution.Text = "屏幕分辨率: 无设备";
        }
        #endregion

        #region 屏幕显示
        private void OnScreenDataReceived(byte[] imageData)
        {
            Dispatcher.Invoke(() =>
            {
                try
                {
                    using (var stream = new MemoryStream(imageData))
                    {
                        var bitmap = new BitmapImage();
                        bitmap.BeginInit();
                        bitmap.StreamSource = stream;
                        bitmap.CacheOption = BitmapCacheOption.OnLoad;
                        bitmap.EndInit();
                        bitmap.Freeze();

                        screenImage.Source = bitmap;
                        // 设置图像大小与画布大小一致，Viewbox会自动缩放到适合的显示区域
                        screenImage.Width = screenCanvas.Width;
                        screenImage.Height = screenCanvas.Height;

                        // 计算FPS
                        _frameCount++;
                        var now = DateTime.Now;
                        if ((now - _lastFrameTime).TotalSeconds >= 1.0)
                        {
                            txtFPS.Text = $"FPS: {_frameCount}";
                            _frameCount = 0;
                            _lastFrameTime = now;
                        }
                    }
                }
                catch (Exception ex)
                {
                    txtStatus.Text = $"显示图片失败: {ex.Message}";
                }
            });
        }

        // 这个方法现在不需要了，因为Android端会自动发送屏幕数据
        private async void BtnScreenshot_Click(object sender, RoutedEventArgs e)
        {
            // 可以用来请求Android端调整发送频率或质量
            txtStatus.Text = "Android端会自动发送屏幕数据";
        }

        private void RefreshTimer_Tick(object sender, EventArgs e)
        {
            // 更新连接状态信息等
        }
        #endregion

        #region 触摸事件处理
        private async void ScreenImage_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!_server.IsClientConnected) return;

            _isLeftMousePressed = true;
            _lastMousePosition = e.GetPosition(screenImage);

            // 左键点击：直接发送tap命令
            var scaledPoint = ScalePointToDevice(_lastMousePosition);
            await _server.SendTouchCommandAsync("tap", (int)scaledPoint.X, (int)scaledPoint.Y);
        }

        private void ScreenImage_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            _isLeftMousePressed = false;
        }

        private async void ScreenImage_MouseMove(object sender, MouseEventArgs e)
        {
            // 右键拖拽滑动
            if (_isRightMousePressed && e.RightButton == MouseButtonState.Pressed)
            {
                var currentPosition = e.GetPosition(screenImage);
                var scaledStart = ScalePointToDevice(_lastMousePosition);
                var scaledEnd = ScalePointToDevice(currentPosition);

                // 发送滑动指令
                await _server.SendTouchCommandAsync("swipe",
                    (int)scaledStart.X, (int)scaledStart.Y, 100,
                    (int)scaledEnd.X, (int)scaledEnd.Y);

                _lastMousePosition = currentPosition;
            }
        }

        private void ScreenImage_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (!_server.IsClientConnected) return;

            // 右键按下：开始滑动模式
            _isRightMousePressed = true;
            _lastMousePosition = e.GetPosition(screenImage);
            screenImage.CaptureMouse();
        }

        private void ScreenImage_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (_isRightMousePressed)
            {
                _isRightMousePressed = false;
                screenImage.ReleaseMouseCapture();
            }
        }

        private Point ScalePointToDevice(Point screenPoint)
        {
            if (_server.DeviceInfo == null) return screenPoint;

            var scaleX = (double)_server.DeviceInfo.ScreenWidth / screenImage.ActualWidth;
            var scaleY = (double)_server.DeviceInfo.ScreenHeight / screenImage.ActualHeight;

            return new Point(screenPoint.X * scaleX, screenPoint.Y * scaleY);
        }
        #endregion

        #region 快捷按键
        private async void BtnHome_Click(object sender, RoutedEventArgs e)
        {
            await _server.SendKeyCommandAsync(3); // Android Home键
        }

        private async void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            await _server.SendKeyCommandAsync(4); // Android Back键
        }

        private async void BtnMenu_Click(object sender, RoutedEventArgs e)
        {
            await _server.SendKeyCommandAsync(82); // Android Menu键
        }

        private async void BtnPower_Click(object sender, RoutedEventArgs e)
        {
            await _server.SendKeyCommandAsync(26); // Android Power键
        }

        private async void BtnVolumeUp_Click(object sender, RoutedEventArgs e)
        {
            await _server.SendKeyCommandAsync(24); // Android VolumeUp键
        }

        private async void BtnVolumeDown_Click(object sender, RoutedEventArgs e)
        {
            await _server.SendKeyCommandAsync(25); // Android VolumeDown键
        }

        private async void BtnSendText_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(txtInput.Text))
            {
                await _server.SendTextCommandAsync(txtInput.Text);
                txtInput.Clear();
            }
        }
        #endregion

        #region 事件处理
        private void OnErrorOccurred(string error)
        {
            Dispatcher.Invoke(() =>
            {
                txtStatus.Text = $"错误: {error}";
                MessageBox.Show(error, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            });
        }

        private void OnStatusMessageReceived(string message)
        {
            Dispatcher.Invoke(() =>
            {
                txtStatus.Text = message;
            });
        }
        #endregion

        #region 界面控制
        private void EnableControlButtons(bool enabled)
        {
            btnScreenshot.IsEnabled = enabled;
            btnHome.IsEnabled = enabled;
            btnBack.IsEnabled = enabled;
            btnMenu.IsEnabled = enabled;
            btnPower.IsEnabled = enabled;
            btnVolumeUp.IsEnabled = enabled;
            btnVolumeDown.IsEnabled = enabled;
            btnSendText.IsEnabled = enabled;
        }
        #endregion

        protected override void OnClosed(EventArgs e)
        {
            _refreshTimer?.Stop();
            _server?.StopServerAsync();
            base.OnClosed(e);
        }
    }
}