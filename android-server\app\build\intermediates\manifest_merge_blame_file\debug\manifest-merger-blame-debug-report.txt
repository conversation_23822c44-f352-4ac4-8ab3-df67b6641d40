1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.remotecontrol.android"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- 前台服务权限 -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
18-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:12:5-94
18-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:12:22-91
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:13:5-68
19-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:13:22-65
20
21    <!-- 防止应用被杀死 -->
22    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
22-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:16:5-95
22-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:16:22-92
23
24    <!-- 屏幕录制和截图权限 -->
25    <uses-permission
25-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:19:5-20:38
26        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
26-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:19:22-78
27        android:maxSdkVersion="28" />
27-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:20:9-35
28    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
28-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:21:5-80
28-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:21:22-77
29
30    <!-- 系统级别权限（需要root或系统签名） -->
31    <uses-permission android:name="android.permission.INJECT_EVENTS" />
31-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:24:5-25:47
31-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:24:22-69
32    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
32-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:26:5-78
32-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:26:22-75
33
34    <!-- 无障碍服务权限 -->
35    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
35-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:29:5-30:47
35-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:29:22-82
36
37    <permission
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
38        android:name="com.remotecontrol.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.remotecontrol.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
42
43    <application
43-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:32:5-75:19
44        android:allowBackup="true"
44-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:33:9-35
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
46        android:dataExtractionRules="@xml/data_extraction_rules"
46-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:34:9-65
47        android:debuggable="true"
48        android:fullBackupContent="@xml/backup_rules"
48-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:35:9-54
49        android:icon="@drawable/ic_launcher"
49-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:36:9-45
50        android:label="@string/app_name"
50-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:37:9-41
51        android:roundIcon="@drawable/ic_launcher"
51-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:38:9-50
52        android:supportsRtl="true"
52-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:39:9-35
53        android:theme="@style/Theme.AndroidRemoteServer"
53-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:40:9-57
54        android:usesCleartextTraffic="true" >
54-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:41:9-44
55
56        <!-- 主Activity -->
57        <activity
57-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:45:9-53:20
58            android:name="com.remotecontrol.android.MainActivity"
58-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:46:13-41
59            android:exported="true"
59-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:47:13-36
60            android:screenOrientation="portrait" >
60-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:48:13-49
61            <intent-filter>
61-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:49:13-52:29
62                <action android:name="android.intent.action.MAIN" />
62-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:50:17-69
62-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:50:25-66
63
64                <category android:name="android.intent.category.LAUNCHER" />
64-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:51:17-77
64-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:51:27-74
65            </intent-filter>
66        </activity>
67
68        <!-- 远程控制服务 -->
69        <service
69-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:56:9-60:63
70            android:name="com.remotecontrol.android.service.RemoteControlService"
70-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:57:13-57
71            android:enabled="true"
71-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:58:13-35
72            android:exported="false"
72-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:59:13-37
73            android:foregroundServiceType="mediaProjection" />
73-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:60:13-60
74
75        <!-- 无障碍服务 -->
76        <service
76-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:63:9-73:19
77            android:name="com.remotecontrol.android.service.AccessibilityService"
77-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:64:13-57
78            android:exported="true"
78-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:66:13-36
79            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
79-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:65:13-79
80            <intent-filter>
80-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:67:13-69:29
81                <action android:name="android.accessibilityservice.AccessibilityService" />
81-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:68:17-92
81-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:68:25-89
82            </intent-filter>
83
84            <meta-data
84-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:70:13-72:72
85                android:name="android.accessibilityservice"
85-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:71:17-60
86                android:resource="@xml/accessibility_service_config" />
86-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:72:17-69
87        </service>
88
89        <activity
89-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
90            android:name="com.karumi.dexter.DexterActivity"
90-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
91            android:theme="@style/Dexter.Internal.Theme.Transparent" />
91-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
92
93        <provider
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
94            android:name="androidx.startup.InitializationProvider"
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
95            android:authorities="com.remotecontrol.android.androidx-startup"
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
96            android:exported="false" >
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
97            <meta-data
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.emoji2.text.EmojiCompatInitializer"
98-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
99                android:value="androidx.startup" />
99-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
100            <meta-data
100-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
101                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
101-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
102                android:value="androidx.startup" />
102-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
103            <meta-data
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
104                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
105                android:value="androidx.startup" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
106        </provider>
107
108        <receiver
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
109            android:name="androidx.profileinstaller.ProfileInstallReceiver"
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
110            android:directBootAware="false"
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
111            android:enabled="true"
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
112            android:exported="true"
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
113            android:permission="android.permission.DUMP" >
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
114            <intent-filter>
114-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
115                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
116            </intent-filter>
117            <intent-filter>
117-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
118                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
119            </intent-filter>
120            <intent-filter>
120-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
121                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
122            </intent-filter>
123            <intent-filter>
123-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
124                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
124-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
125            </intent-filter>
126        </receiver>
127    </application>
128
129</manifest>
