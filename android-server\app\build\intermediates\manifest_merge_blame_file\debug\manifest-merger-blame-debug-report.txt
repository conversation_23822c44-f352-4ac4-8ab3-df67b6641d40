1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.remotecontrol.android"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
8-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
9        android:targetSdkVersion="34" />
9-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:8:5-76
14-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:8:22-73
15
16    <!-- 前台服务权限 -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:11:5-77
17-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:11:22-74
18    <uses-permission android:name="android.permission.WAKE_LOCK" />
18-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:12:5-68
18-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:12:22-65
19
20    <!-- 防止应用被杀死 -->
21    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
21-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:15:5-95
21-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:15:22-92
22
23    <!-- 屏幕录制和截图权限 -->
24    <uses-permission
24-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:18:5-19:38
25        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
25-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:18:22-78
26        android:maxSdkVersion="28" />
26-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:19:9-35
27    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
27-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:20:5-80
27-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:20:22-77
28
29    <!-- 系统级别权限（需要root或系统签名） -->
30    <uses-permission android:name="android.permission.INJECT_EVENTS" />
30-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:23:5-24:47
30-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:23:22-69
31    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
31-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:25:5-78
31-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:25:22-75
32
33    <!-- 无障碍服务权限 -->
34    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
34-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:28:5-29:47
34-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:28:22-82
35
36    <permission
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
37        android:name="com.remotecontrol.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
37-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
38        android:protectionLevel="signature" />
38-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
39
40    <uses-permission android:name="com.remotecontrol.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
40-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
41
42    <application
42-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:31:5-73:19
43        android:allowBackup="true"
43-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:32:9-35
44        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
44-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
45        android:dataExtractionRules="@xml/data_extraction_rules"
45-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:33:9-65
46        android:debuggable="true"
47        android:fullBackupContent="@xml/backup_rules"
47-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:34:9-54
48        android:icon="@drawable/ic_launcher"
48-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:35:9-45
49        android:label="@string/app_name"
49-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:36:9-41
50        android:roundIcon="@drawable/ic_launcher"
50-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:37:9-50
51        android:supportsRtl="true"
51-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:38:9-35
52        android:theme="@style/Theme.AndroidRemoteServer"
52-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:39:9-57
53        android:usesCleartextTraffic="true" >
53-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:40:9-44
54
55        <!-- 主Activity -->
56        <activity
56-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:44:9-52:20
57            android:name="com.remotecontrol.android.MainActivity"
57-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:45:13-41
58            android:exported="true"
58-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:46:13-36
59            android:screenOrientation="portrait" >
59-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:47:13-49
60            <intent-filter>
60-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:48:13-51:29
61                <action android:name="android.intent.action.MAIN" />
61-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:49:17-69
61-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:49:25-66
62
63                <category android:name="android.intent.category.LAUNCHER" />
63-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:50:17-77
63-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:50:27-74
64            </intent-filter>
65        </activity>
66
67        <!-- 远程控制服务 -->
68        <service
68-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:55:9-58:40
69            android:name="com.remotecontrol.android.service.RemoteControlService"
69-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:56:13-57
70            android:enabled="true"
70-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:57:13-35
71            android:exported="false" />
71-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:58:13-37
72
73        <!-- 无障碍服务 -->
74        <service
74-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:61:9-71:19
75            android:name="com.remotecontrol.android.service.AccessibilityService"
75-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:62:13-57
76            android:exported="true"
76-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:64:13-36
77            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE" >
77-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:63:13-79
78            <intent-filter>
78-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:65:13-67:29
79                <action android:name="android.accessibilityservice.AccessibilityService" />
79-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:66:17-92
79-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:66:25-89
80            </intent-filter>
81
82            <meta-data
82-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:68:13-70:72
83                android:name="android.accessibilityservice"
83-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:69:17-60
84                android:resource="@xml/accessibility_service_config" />
84-->C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:70:17-69
85        </service>
86
87        <activity
87-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
88            android:name="com.karumi.dexter.DexterActivity"
88-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
89            android:theme="@style/Dexter.Internal.Theme.Transparent" />
89-->[com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
90
91        <provider
91-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
92            android:name="androidx.startup.InitializationProvider"
92-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
93            android:authorities="com.remotecontrol.android.androidx-startup"
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
94            android:exported="false" >
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.emoji2.text.EmojiCompatInitializer"
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
97                android:value="androidx.startup" />
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
101            <meta-data
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
102                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
103                android:value="androidx.startup" />
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
104        </provider>
105
106        <receiver
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
107            android:name="androidx.profileinstaller.ProfileInstallReceiver"
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
108            android:directBootAware="false"
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
109            android:enabled="true"
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
110            android:exported="true"
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
111            android:permission="android.permission.DUMP" >
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
113                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
116                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
119                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
120            </intent-filter>
121            <intent-filter>
121-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
122                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
122-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
123            </intent-filter>
124        </receiver>
125    </application>
126
127</manifest>
