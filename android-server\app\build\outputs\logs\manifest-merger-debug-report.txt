-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\edcdfc95954d32337831dbe89ac958a3\transformed\viewbinding-7.4.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\10dc035393e004282fd64c0079b394e4\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01f1cc7a2abfc49c37fa350c0d231552\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eac77de5d1edb1287ee9a266e7e83489\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c5bc1c461f48fbd1a3b03cc22fccf5ce\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4640af8cfe4033b26e384ace18daf9fe\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\00e8a55136748642c695f2ed4295a00a\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\40d46c70d7f151d8994e56a5e38bca68\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\42344edbf1610b518d483454f2e7df60\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9898046eb232dd31d1e27d747946860f\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9bdd1429e37ebc4de6b42d637fc97e8a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\996206034fcb4334ab59d4a42c517b3c\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e48d1792e695ec11b7b8465b93ad75b8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bb2f5788438f29a968fb76cfc975899\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eba112c16f97002fece0ae77f89aa4eb\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d186f9c3d237a211a3d28863da343d0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c259403097c4ddcb6361b627efa13593\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbf1cd9f9aad422ef8b75a478590ecf5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa7f467c00e17b61bb8d6f7ca19360f9\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\eca80109b29479a0617e83ab5a7ada60\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\28ce5c38b1a91ff16aad9968c96eeb5b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9ce4178d469d5b2e24a653558f24dbf\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\70d35e12cafe4860be7ab1477dc57c69\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d7e9d73cc66a945ad86bb3bfaeb8141\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c71badccda85e7a58d1d17161e92254d\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\022bc8ba904350a71063467cbf0a2171\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2849d2eac0d9fde6786026808536dfe3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ce7dd859d86bbf1e8c4a1ec1eda9be8\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\33fb2ccea7fa4a3500fce9709a317aa2\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1e7767a311a150523bbd97d7fa50c2a\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a37d996378a7ad1c46ec757263822340\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b527cb51125386cf1e185ba3bad7eb8\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4958774dec239a1adb54edcafff0fe7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\579fc2d64f365a7bd8d282dcb2c73039\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\410a8d1196dd09ee224e9238728d9ae9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\35a1b43430b49738104dbea6b7331ca1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1df000dad7123a7b4ee8681a2894e157\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\25426b3f6f76cffe539eb72e997f8eb9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60cb0e9c4b54370ddeb5022998d12273\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\740ad4c36fbe660aa0fd7098afa1b510\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\39bdd8cad7e6966b8be0c288cc5335c3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac65ab2a2b8fb6c9d8693dfac65ccaeb\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be7e2dd1a835c1922c0f008a4063736\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
	package
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:1-77:12
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:8:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:8:22-73
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:11:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:11:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:12:5-94
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:12:22-91
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:13:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:13:22-65
uses-permission#android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:16:5-95
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:16:22-92
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:19:5-20:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:20:9-35
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:19:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:21:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:21:22-77
uses-permission#android.permission.INJECT_EVENTS
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:24:5-25:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:25:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:24:22-69
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:26:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:26:22-75
uses-permission#android.permission.BIND_ACCESSIBILITY_SERVICE
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:29:5-30:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:30:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:29:22-82
application
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:32:5-75:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:26:5-30:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\10dc035393e004282fd64c0079b394e4\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\10dc035393e004282fd64c0079b394e4\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01f1cc7a2abfc49c37fa350c0d231552\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01f1cc7a2abfc49c37fa350c0d231552\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\35a1b43430b49738104dbea6b7331ca1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\35a1b43430b49738104dbea6b7331ca1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\25426b3f6f76cffe539eb72e997f8eb9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\25426b3f6f76cffe539eb72e997f8eb9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:39:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:37:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:35:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:38:9-50
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:42:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:36:9-45
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:33:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:40:9-57
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:34:9-65
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:41:9-44
activity#com.remotecontrol.android.MainActivity
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:45:9-53:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:48:13-49
	android:exported
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:47:13-36
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:46:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:49:13-52:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:50:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:50:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:51:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:51:27-74
service#com.remotecontrol.android.service.RemoteControlService
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:56:9-60:63
	android:enabled
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:58:13-35
	android:exported
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:59:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:60:13-60
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:57:13-57
service#com.remotecontrol.android.service.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:63:9-73:19
	android:exported
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:66:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:65:13-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:64:13-57
intent-filter#action:name:android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:67:13-69:29
action#android.accessibilityservice.AccessibilityService
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:68:17-92
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:68:25-89
meta-data#android.accessibilityservice
ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:70:13-72:72
	android:resource
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:72:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml:71:17-60
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\edcdfc95954d32337831dbe89ac958a3\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:7.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\edcdfc95954d32337831dbe89ac958a3\transformed\viewbinding-7.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:22:5-24:41
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\10dc035393e004282fd64c0079b394e4\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\10dc035393e004282fd64c0079b394e4\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01f1cc7a2abfc49c37fa350c0d231552\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\01f1cc7a2abfc49c37fa350c0d231552\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eac77de5d1edb1287ee9a266e7e83489\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eac77de5d1edb1287ee9a266e7e83489\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c5bc1c461f48fbd1a3b03cc22fccf5ce\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\c5bc1c461f48fbd1a3b03cc22fccf5ce\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4640af8cfe4033b26e384ace18daf9fe\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4640af8cfe4033b26e384ace18daf9fe\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\00e8a55136748642c695f2ed4295a00a\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\00e8a55136748642c695f2ed4295a00a\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\40d46c70d7f151d8994e56a5e38bca68\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\40d46c70d7f151d8994e56a5e38bca68\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\42344edbf1610b518d483454f2e7df60\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\42344edbf1610b518d483454f2e7df60\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9898046eb232dd31d1e27d747946860f\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9898046eb232dd31d1e27d747946860f\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9bdd1429e37ebc4de6b42d637fc97e8a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\9bdd1429e37ebc4de6b42d637fc97e8a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\996206034fcb4334ab59d4a42c517b3c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\996206034fcb4334ab59d4a42c517b3c\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e48d1792e695ec11b7b8465b93ad75b8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e48d1792e695ec11b7b8465b93ad75b8\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bb2f5788438f29a968fb76cfc975899\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9bb2f5788438f29a968fb76cfc975899\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eba112c16f97002fece0ae77f89aa4eb\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\eba112c16f97002fece0ae77f89aa4eb\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d186f9c3d237a211a3d28863da343d0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d186f9c3d237a211a3d28863da343d0\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c259403097c4ddcb6361b627efa13593\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c259403097c4ddcb6361b627efa13593\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbf1cd9f9aad422ef8b75a478590ecf5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fbf1cd9f9aad422ef8b75a478590ecf5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa7f467c00e17b61bb8d6f7ca19360f9\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa7f467c00e17b61bb8d6f7ca19360f9\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\eca80109b29479a0617e83ab5a7ada60\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\eca80109b29479a0617e83ab5a7ada60\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\28ce5c38b1a91ff16aad9968c96eeb5b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\28ce5c38b1a91ff16aad9968c96eeb5b\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9ce4178d469d5b2e24a653558f24dbf\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c9ce4178d469d5b2e24a653558f24dbf\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\70d35e12cafe4860be7ab1477dc57c69\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\70d35e12cafe4860be7ab1477dc57c69\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d7e9d73cc66a945ad86bb3bfaeb8141\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d7e9d73cc66a945ad86bb3bfaeb8141\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c71badccda85e7a58d1d17161e92254d\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\c71badccda85e7a58d1d17161e92254d\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\022bc8ba904350a71063467cbf0a2171\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\022bc8ba904350a71063467cbf0a2171\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2849d2eac0d9fde6786026808536dfe3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\2849d2eac0d9fde6786026808536dfe3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ce7dd859d86bbf1e8c4a1ec1eda9be8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ce7dd859d86bbf1e8c4a1ec1eda9be8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\33fb2ccea7fa4a3500fce9709a317aa2\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\33fb2ccea7fa4a3500fce9709a317aa2\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1e7767a311a150523bbd97d7fa50c2a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1e7767a311a150523bbd97d7fa50c2a\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a37d996378a7ad1c46ec757263822340\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a37d996378a7ad1c46ec757263822340\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b527cb51125386cf1e185ba3bad7eb8\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0b527cb51125386cf1e185ba3bad7eb8\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4958774dec239a1adb54edcafff0fe7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f4958774dec239a1adb54edcafff0fe7\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\579fc2d64f365a7bd8d282dcb2c73039\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\579fc2d64f365a7bd8d282dcb2c73039\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\410a8d1196dd09ee224e9238728d9ae9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\410a8d1196dd09ee224e9238728d9ae9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\35a1b43430b49738104dbea6b7331ca1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\35a1b43430b49738104dbea6b7331ca1\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1df000dad7123a7b4ee8681a2894e157\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\1df000dad7123a7b4ee8681a2894e157\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\25426b3f6f76cffe539eb72e997f8eb9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\25426b3f6f76cffe539eb72e997f8eb9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60cb0e9c4b54370ddeb5022998d12273\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60cb0e9c4b54370ddeb5022998d12273\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\740ad4c36fbe660aa0fd7098afa1b510\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\740ad4c36fbe660aa0fd7098afa1b510\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\39bdd8cad7e6966b8be0c288cc5335c3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\39bdd8cad7e6966b8be0c288cc5335c3\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac65ab2a2b8fb6c9d8693dfac65ccaeb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac65ab2a2b8fb6c9d8693dfac65ccaeb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be7e2dd1a835c1922c0f008a4063736\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\7be7e2dd1a835c1922c0f008a4063736\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
		INJECTED from C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\AndroidManifest.xml
activity#com.karumi.dexter.DexterActivity
ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:27:9-29:72
	android:theme
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.karumi:dexter:6.2.3] C:\Users\<USER>\.gradle\caches\transforms-3\810f2bba0dcbff0a983662892944a969\transformed\dexter-6.2.3\AndroidManifest.xml:28:13-60
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\25426b3f6f76cffe539eb72e997f8eb9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\25426b3f6f76cffe539eb72e997f8eb9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\7eac7c4b7c7f620eb0c068bc211c7a9d\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\7d4c366ccf2cc2224580ed379806a7ae\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.remotecontrol.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.remotecontrol.android.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\c6dc7adb4bd25c9f6fecda703177518b\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\f909ffef40f93412707651f6659c9cfe\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
