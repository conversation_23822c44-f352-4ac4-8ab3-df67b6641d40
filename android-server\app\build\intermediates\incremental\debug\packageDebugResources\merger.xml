<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res"/><source path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res"><file name="ic_launcher" path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\drawable\ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_notification" path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\drawable\ic_notification.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Android远程控制服务端</string><string name="start_service">启动服务</string><string name="stop_service">停止服务</string><string name="service_status">服务状态</string><string name="service_running">服务运行中</string><string name="service_stopped">服务已停止</string><string name="ip_address">IP地址</string><string name="port">端口</string><string name="connected_clients">已连接客户端</string><string name="no_clients">无客户端连接</string><string name="permission_required">需要权限</string><string name="accessibility_permission">无障碍权限</string><string name="media_projection_permission">屏幕录制权限</string><string name="system_alert_permission">悬浮窗权限</string><string name="grant_permission">授予权限</string><string name="permission_denied">权限被拒绝</string><string name="server_error">服务器错误</string><string name="client_connected">客户端已连接</string><string name="client_disconnected">客户端已断开</string></file><file path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.AndroidRemoteServer" parent="Theme.Material3.DayNight">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor" ns1:targetApi="l">?attr/colorPrimaryVariant</item>
    </style></file><file name="accessibility_service_config" path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\xml\accessibility_service_config.xml" qualifiers="" type="xml"/><file name="backup_rules" path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source><source path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\build\generated\res\rs\debug"/><source path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\uhhiuo\android-server\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"/><mergedItems/></merger>