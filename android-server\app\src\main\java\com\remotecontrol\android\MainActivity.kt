package com.remotecontrol.android

import android.Manifest
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.media.projection.MediaProjectionManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.karumi.dexter.Dexter
import com.karumi.dexter.MultiplePermissionsReport
import com.karumi.dexter.PermissionToken
import com.karumi.dexter.listener.PermissionRequest
import com.karumi.dexter.listener.multi.MultiplePermissionsListener
import com.remotecontrol.android.service.RemoteControlService
import com.remotecontrol.android.utils.NetworkUtils

class MainActivity : AppCompatActivity() {
    
    private lateinit var txtServiceStatus: TextView
    private lateinit var btnStartService: Button
    private lateinit var btnStopService: Button
    private lateinit var txtIpAddress: TextView
    private lateinit var txtPort: TextView
    private lateinit var txtConnectedClients: TextView
    private lateinit var btnAccessibilityPermission: Button
    private lateinit var btnMediaProjectionPermission: Button
    private lateinit var btnSystemAlertPermission: Button
    
    private var mediaProjectionManager: MediaProjectionManager? = null
    
    // 权限请求广播接收器
    private val permissionRequestReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                "com.remotecontrol.android.REQUEST_SCREEN_PERMISSION" -> {
                    requestScreenCapturePermission()
                }
            }
        }
    }
    
    // 屏幕录制权限请求
    private val screenCaptureRequest = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val data = result.data
            if (data != null) {
                RemoteControlService.setMediaProjectionData(result.resultCode, data)
                updatePermissionStatus()
                showToast("屏幕录制权限已授予")
                
                // 通知服务权限已授予
                val intent = Intent("com.remotecontrol.android.SCREEN_PERMISSION_GRANTED")
                sendBroadcast(intent)
            }
        } else {
            showToast("屏幕录制权限被拒绝")
            
            // 通知服务权限被拒绝
            val intent = Intent("com.remotecontrol.android.SCREEN_PERMISSION_DENIED")
            sendBroadcast(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initViews()
        initData()
        setupClickListeners()
        updatePermissionStatus()
        updateServiceStatus()
    }

    private fun initViews() {
        txtServiceStatus = findViewById(R.id.txtServiceStatus)
        btnStartService = findViewById(R.id.btnStartService)
        btnStopService = findViewById(R.id.btnStopService)
        txtIpAddress = findViewById(R.id.txtIpAddress)
        txtPort = findViewById(R.id.txtPort)
        txtConnectedClients = findViewById(R.id.txtConnectedClients)
        btnAccessibilityPermission = findViewById(R.id.btnAccessibilityPermission)
        btnMediaProjectionPermission = findViewById(R.id.btnMediaProjectionPermission)
        btnSystemAlertPermission = findViewById(R.id.btnSystemAlertPermission)
        
        mediaProjectionManager = getSystemService(MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
    }
    
    private fun initData() {
        // 设置默认IP地址
        txtIpAddress.text = "***********"
        
        // 显示端口号
        txtPort.text = "8888"
    }
    
    private fun setupClickListeners() {
        btnStartService.setOnClickListener {
            if (checkBasicPermissions()) {
                val ip = txtIpAddress.text.toString().trim()
                if (ip.isEmpty()) {
                    showToast("请检查IP地址")
                    return@setOnClickListener
                }
                startRemoteService()
            } else {
                showToast("请先授予无障碍权限和悬浮窗权限")
            }
        }
        
        btnStopService.setOnClickListener {
            stopRemoteService()
        }
        
        btnMediaProjectionPermission.setOnClickListener {
            requestScreenCapturePermission()
        }
        
        btnAccessibilityPermission.setOnClickListener {
            openAccessibilitySettings()
        }
        
        btnSystemAlertPermission.setOnClickListener {
            requestSystemAlertPermission()
        }
    }
    
    private fun startRemoteService() {
        val pcIP = txtIpAddress.text.toString().trim()
        val pcPort = txtPort.text.toString().trim()
        
        try {
            val port = pcPort.toInt()
            if (port < 1 || port > 65535) {
                showToast("端口号范围应为1-65535")
                return
            }
            
            val intent = Intent(this, RemoteControlService::class.java).apply {
                action = RemoteControlService.ACTION_CONNECT
                putExtra("pc_ip", pcIP)
                putExtra("pc_port", port)
            }
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                startForegroundService(intent)
            } else {
                startService(intent)
            }
            
            updateServiceStatus()
            showToast("正在连接到 $pcIP:$port...")
            
        } catch (e: NumberFormatException) {
            showToast("端口号格式错误")
        }
    }
    
    private fun stopRemoteService() {
        val intent = Intent(this, RemoteControlService::class.java).apply {
            action = RemoteControlService.ACTION_STOP
        }
        startService(intent)
        
        updateServiceStatus()
        showToast("远程控制服务已停止")
    }
    
    private fun checkAllPermissions(): Boolean {
        return hasScreenCapturePermission() && 
               hasAccessibilityPermission() && 
               hasSystemAlertPermission()
    }
    
    private fun checkBasicPermissions(): Boolean {
        return hasAccessibilityPermission() && hasSystemAlertPermission()
    }
    
    private fun hasScreenCapturePermission(): Boolean {
        return RemoteControlService.hasMediaProjectionData()
    }
    
    private fun hasAccessibilityPermission(): Boolean {
        try {
            val accessibilityEnabled = Settings.Secure.getInt(
                contentResolver,
                Settings.Secure.ACCESSIBILITY_ENABLED
            )
            if (accessibilityEnabled == 1) {
                val services = Settings.Secure.getString(
                    contentResolver,
                    Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES
                )
                return services?.contains(packageName) == true
            }
        } catch (e: Settings.SettingNotFoundException) {
            // 设置未找到，返回false
        }
        return false
    }
    
    private fun hasSystemAlertPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(this)
        } else {
            true
        }
    }
    
    private fun requestScreenCapturePermission() {
        if (mediaProjectionManager != null) {
            val captureIntent = mediaProjectionManager!!.createScreenCaptureIntent()
            screenCaptureRequest.launch(captureIntent)
        }
    }
    
    private fun openAccessibilitySettings() {
        try {
            val intent = Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
            startActivity(intent)
            showToast("请找到并启用本应用的无障碍服务")
        } catch (e: Exception) {
            showToast("无法打开无障碍设置")
        }
    }
    
    private fun requestSystemAlertPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!Settings.canDrawOverlays(this)) {
                val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                    data = Uri.parse("package:$packageName")
                }
                try {
                    startActivity(intent)
                    showToast("请允许显示在其他应用上层")
                } catch (e: Exception) {
                    showToast("无法打开悬浮窗权限设置")
                }
            } else {
                showToast("悬浮窗权限已授予")
            }
        }
    }
    
    private fun updatePermissionStatus() {
        // 更新权限按钮状态
        btnMediaProjectionPermission.text = if (hasScreenCapturePermission()) 
            "✓ 屏幕录制权限已授予" else "请求屏幕录制权限"
        btnMediaProjectionPermission.isEnabled = !hasScreenCapturePermission()
        
        btnAccessibilityPermission.text = if (hasAccessibilityPermission()) 
            "✓ 无障碍权限已授予" else "请求无障碍权限"
        btnAccessibilityPermission.isEnabled = !hasAccessibilityPermission()
        
        btnSystemAlertPermission.text = if (hasSystemAlertPermission()) 
            "✓ 悬浮窗权限已授予" else "请求悬浮窗权限"
        btnSystemAlertPermission.isEnabled = !hasSystemAlertPermission()
    }
    
    private fun updateServiceStatus() {
        // 检查服务运行状态
        val isServiceRunning = RemoteControlService.isServiceRunning()
        btnStartService.isEnabled = !isServiceRunning
        btnStopService.isEnabled = isServiceRunning
        
        if (isServiceRunning) {
            txtServiceStatus.text = "服务运行中"
            txtConnectedClients.text = "等待客户端连接..."
        } else {
            txtServiceStatus.text = "服务已停止"
            txtConnectedClients.text = "无客户端连接"
        }
    }
    
    override fun onResume() {
        super.onResume()
        updatePermissionStatus()
        updateServiceStatus()
        
        // 注册广播接收器
        val filter = IntentFilter("com.remotecontrol.android.REQUEST_SCREEN_PERMISSION")
        registerReceiver(permissionRequestReceiver, filter)
    }
    
    override fun onPause() {
        super.onPause()
        // 注销广播接收器
        try {
            unregisterReceiver(permissionRequestReceiver)
        } catch (e: Exception) {
            // 忽略重复注销的错误
        }
    }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }
}