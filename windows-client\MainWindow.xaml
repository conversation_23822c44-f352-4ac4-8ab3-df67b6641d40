<Window x:Class="AndroidRemoteClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Android远程控制客户端" Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 工具栏 -->
        <Border Grid.Row="0" Background="#FF2196F3" Padding="10">
            <StackPanel Orientation="Horizontal">
                <Label Content="监听IP:" Foreground="White" VerticalAlignment="Center"/>
                <ComboBox Name="cmbListenIP" Width="140" VerticalAlignment="Center" IsEditable="True">
                    <ComboBoxItem Content="0.0.0.0 (所有网卡)" IsSelected="True"/>
                    <ComboBoxItem Content="127.0.0.1 (本地回环)"/>
                </ComboBox>
                <Label Content="端口:" Foreground="White" VerticalAlignment="Center" Margin="10,0,0,0"/>
                <TextBox Name="txtPort" Width="80" Text="8888" VerticalAlignment="Center"/>
                <Button Name="btnStartServer" Content="启动服务" Click="BtnStartServer_Click" Background="#FF4CAF50" Margin="10,0,0,0"/>
                <Button Name="btnStopServer" Content="停止服务" Click="BtnStopServer_Click" Background="#FFF44336" IsEnabled="False"/>
                <Label Content="实际IP:" Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
                <Label Name="lblServerIP" Content="未启动" Foreground="White" VerticalAlignment="Center"/>
                <Label Name="lblConnectionStatus" Content="无设备连接" Foreground="White" VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>
            
            <!-- 屏幕显示区域 -->
            <Border Grid.Column="0" Background="Black" Margin="10" Name="screenBorder">
                <Grid>
                    <Viewbox Stretch="UniformToFill" StretchDirection="Both"
                             HorizontalAlignment="Left" VerticalAlignment="Top">
                        <Canvas Name="screenCanvas" Background="Gray">
                            <Image x:Name="screenImage"
                                   MouseLeftButtonDown="ScreenImage_MouseLeftButtonDown"
                                   MouseLeftButtonUp="ScreenImage_MouseLeftButtonUp"
                                   MouseMove="ScreenImage_MouseMove"
                                   MouseRightButtonDown="ScreenImage_MouseRightButtonDown"/>
                        </Canvas>
                    </Viewbox>
                    <!-- 透明覆盖层，阻止空白区域的触摸事件 -->
                    <Rectangle Fill="Transparent" IsHitTestVisible="True"/>
                </Grid>
            </Border>
            
            <!-- 控制面板 -->
            <Border Grid.Column="1" Background="#FFF5F5F5" Margin="0,10,10,10">
                <StackPanel Margin="10">
                    <TextBlock Text="设备控制" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                    
                    <!-- 设备信息 -->
                    <GroupBox Header="设备信息" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock Name="lblDeviceModel" Text="型号: 未连接"/>
                            <TextBlock Name="lblAndroidVersion" Text="Android版本: 未连接"/>
                            <TextBlock Name="lblScreenResolution" Text="屏幕分辨率: 未连接"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 屏幕控制 -->
                    <GroupBox Header="屏幕控制" Margin="0,0,0,10">
                        <StackPanel>
                            <Button Name="btnScreenshot" Content="截图" Click="BtnScreenshot_Click" IsEnabled="False"/>
                            <StackPanel Orientation="Horizontal">
                                <Label Content="画质:"/>
                                <Slider Name="sliderQuality" Minimum="10" Maximum="100" Value="80" Width="100" TickFrequency="10" IsSnapToTickEnabled="True"/>
                                <Label Name="lblQuality" Content="80%"/>
                            </StackPanel>
                            <CheckBox Name="chkAutoRefresh" Content="自动刷新" IsChecked="True"/>
                            <StackPanel Orientation="Horizontal">
                                <Label Content="刷新间隔(ms):"/>
                                <TextBox Name="txtRefreshInterval" Text="500" Width="80"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 快捷操作 -->
                    <GroupBox Header="快捷操作" Margin="0,0,0,10">
                        <StackPanel>
                            <Button Name="btnHome" Content="HOME键" Click="BtnHome_Click" IsEnabled="False"/>
                            <Button Name="btnBack" Content="返回键" Click="BtnBack_Click" IsEnabled="False"/>
                            <Button Name="btnMenu" Content="菜单键" Click="BtnMenu_Click" IsEnabled="False"/>
                            <Button Name="btnPower" Content="电源键" Click="BtnPower_Click" IsEnabled="False"/>
                            <Button Name="btnVolumeUp" Content="音量+" Click="BtnVolumeUp_Click" IsEnabled="False"/>
                            <Button Name="btnVolumeDown" Content="音量-" Click="BtnVolumeDown_Click" IsEnabled="False"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <!-- 文本输入 -->
                    <GroupBox Header="文本输入">
                        <StackPanel>
                            <TextBox Name="txtInput" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                            <Button Name="btnSendText" Content="发送文本" Click="BtnSendText_Click" IsEnabled="False"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- 状态栏 -->
        <Border Grid.Row="2" Background="#FFEEEEEE" Padding="10,5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Name="txtStatus" Text="就绪"/>
                <TextBlock Text=" | " Margin="10,0"/>
                <TextBlock Name="txtFPS" Text="FPS: 0"/>
                <TextBlock Text=" | " Margin="10,0"/>
                <TextBlock Name="txtLatency" Text="延迟: 0ms"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>