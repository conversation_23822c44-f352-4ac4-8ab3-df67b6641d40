# Android APK 编译指南

## 🚨 重要说明

由于编译Android APK需要本地Android SDK环境，我无法直接执行编译。请按照以下步骤在您的环境中进行编译。

## 📋 编译前检查

### 1. 环境要求
- ✅ Java JDK 8+ (检测到: Java 21.0.7)
- ✅ Android SDK (路径: C:\Users\<USER>\AppData\Local\Android\Sdk)
- ✅ 项目文件完整
- ⚠️ Gradle Wrapper 需要下载

### 2. 运行环境检查
```batch
cd "C:\Users\<USER>\Desktop\新建文件夹 (13)\android-server"
test_environment.bat
```

## 🔧 编译步骤

### 方法1: 使用构建脚本 (推荐)
```batch
cd "C:\Users\<USER>\Desktop\新建文件夹 (13)\android-server"
build_apk.bat
```

### 方法2: 手动编译
```batch
# 1. 进入项目目录
cd "C:\Users\<USER>\Desktop\新建文件夹 (13)\android-server"

# 2. 给gradlew.bat执行权限 (如果需要)
# 无需额外操作，Windows会自动处理

# 3. 清理项目
gradlew.bat clean

# 4. 编译Debug版本APK
gradlew.bat assembleDebug

# 5. 编译Release版本APK (需要签名配置)
gradlew.bat assembleRelease
```

### 方法3: 使用Android Studio
1. 在Android Studio中打开项目
2. 选择 Build -> Build Bundle(s)/APK(s) -> Build APK(s)
3. 等待编译完成

## 📁 编译输出

编译成功后，APK文件将生成在：
```
app/build/outputs/apk/debug/app-debug.apk          (Debug版本)
app/build/outputs/apk/release/app-release.apk      (Release版本)
```

## 🔧 常见编译问题解决

### 问题1: Gradle Wrapper下载失败
**现象**: gradlew.bat执行失败，提示无法下载gradle-7.4-bin.zip

**解决方案**:
```batch
# 方法1: 使用代理或VPN
set GRADLE_OPTS=-Dhttp.proxyHost=your-proxy -Dhttp.proxyPort=port

# 方法2: 手动下载Gradle
# 1. 从 https://gradle.org/releases/ 下载 gradle-7.4-bin.zip
# 2. 解压到 %GRADLE_USER_HOME%\wrapper\dists\gradle-7.4\xxx\
# 3. 重新运行编译命令
```

### 问题2: Android SDK路径错误
**现象**: 编译时提示找不到Android SDK

**解决方案**:
```batch
# 检查并修正 local.properties 文件中的sdk.dir路径
# 确保路径指向正确的Android SDK安装目录
```

### 问题3: Java版本兼容性
**现象**: 编译时出现Java版本相关错误

**解决方案**:
```batch
# 检查项目使用的Java版本
java -version

# 如果使用Java 17+，可能需要添加JVM参数
set JAVA_OPTS=--add-opens=java.base/java.util=ALL-UNNAMED
```

### 问题4: 依赖下载失败
**现象**: 编译时提示无法下载依赖包

**解决方案**:
```gradle
# 在 build.gradle 中添加国内镜像源
repositories {
    maven { url 'https://maven.aliyun.com/repository/google' }
    maven { url 'https://maven.aliyun.com/repository/central' }
    google()
    mavenCentral()
}
```

## ✅ 编译成功验证

编译成功后，执行以下检查：

```batch
# 1. 检查APK文件是否存在
dir app\build\outputs\apk\debug\app-debug.apk

# 2. 检查APK信息 (需要aapt工具)
aapt dump badging app\build\outputs\apk\debug\app-debug.apk

# 3. 检查APK大小
# 正常大小应该在 5-15MB 之间
```

## 🚀 编译后测试

APK编译成功后，继续进行安装和配置：

```batch
# 1. 运行配置脚本
configure_apk.bat

# 或使用PowerShell脚本
Configure-APK.ps1 -IPAddress "*************" -Port 8888

# 2. 手动安装 (如果脚本失败)
adb install -r app\build\outputs\apk\debug\app-debug.apk
```

## 📊 编译时间预估

首次编译 (包括下载依赖):
- 网络良好: 5-10分钟
- 网络较慢: 10-30分钟

后续编译:
- 增量编译: 1-3分钟
- 完整重新编译: 3-5分钟

## 🔍 编译日志分析

如果编译失败，重点关注以下日志信息：

```
FAILURE: Build failed with an exception.

* What went wrong:
[关键错误信息]

* Try:
[建议解决方案]
```

常见错误类型:
1. **依赖解析失败**: 网络问题或仓库配置错误
2. **编译错误**: 代码语法错误或API使用错误  
3. **资源错误**: 资源文件格式或引用错误
4. **权限错误**: Manifest权限配置问题

## 📞 获取帮助

如果遇到编译问题:

1. **查看完整错误日志**
   ```batch
   gradlew.bat assembleDebug --stacktrace --info
   ```

2. **检查Android Studio Build窗口的详细信息**

3. **常用调试命令**
   ```batch
   # 清理项目
   gradlew.bat clean
   
   # 刷新依赖
   gradlew.bat --refresh-dependencies
   
   # 查看项目信息
   gradlew.bat projects
   
   # 查看任务列表
   gradlew.bat tasks
   ```

---

**重要提示**: 编译过程需要网络连接下载Gradle和Android依赖包，首次编译请确保网络连接稳定。