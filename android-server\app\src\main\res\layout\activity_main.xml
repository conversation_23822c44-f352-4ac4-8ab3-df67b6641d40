<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 应用标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- 服务状态卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/service_status"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/txtServiceStatus"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/service_stopped"
                    android:textSize="16sp"
                    android:textColor="@color/teal_700"
                    android:layout_marginBottom="16dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btnStartService"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/start_service"
                        android:layout_marginEnd="8dp" />

                    <Button
                        android:id="@+id/btnStopService"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/stop_service"
                        android:enabled="false"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 网络信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="网络信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/ip_address"
                        android:textSize="14sp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:id="@+id/txtIpAddress"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="未获取"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/port"
                        android:textSize="14sp"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:id="@+id/txtPort"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="8888"
                        android:textSize="14sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 连接信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/connected_clients"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/txtConnectedClients"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/no_clients"
                    android:textSize="14sp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 权限设置卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/permission_required"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="16dp" />

                <Button
                    android:id="@+id/btnAccessibilityPermission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/accessibility_permission"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <Button
                    android:id="@+id/btnMediaProjectionPermission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/media_projection_permission"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

                <Button
                    android:id="@+id/btnSystemAlertPermission"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/system_alert_permission"
                    style="@style/Widget.Material3.Button.OutlinedButton" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>

</ScrollView>