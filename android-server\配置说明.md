# Android远程控制APK配置说明

## 配置脚本使用方法

### 方法1: 批处理脚本 (简单易用)
```batch
configure_apk.bat
```

**特点:**
- 交互式界面，逐步引导配置
- 自动检查环境和设备
- 支持自动安装和基础配置

### 方法2: PowerShell脚本 (功能强大)
```powershell
# 交互式配置
.\Configure-APK.ps1

# 命令行参数配置
.\Configure-APK.ps1 -IPAddress "*************" -Port 8888 -AutoStart

# 仅安装APK
.\Configure-APK.ps1 -InstallOnly
```

**特点:**
- 支持命令行参数
- 更强大的错误检查
- 彩色输出界面
- 支持批量部署

## 环境要求

### 1. Android SDK
需要安装Android SDK并配置环境变量:
- **aapt** (Android Asset Packaging Tool)
- **adb** (Android Debug Bridge)

#### Windows环境变量配置:
```
PATH 中添加:
C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools
C:\Users\<USER>\AppData\Local\Android\Sdk\build-tools\[版本号]
```

### 2. Android设备设置
- 启用"开发者选项"
- 启用"USB调试"
- 允许计算机的USB调试授权

## 配置参数说明

| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| PC IP地址 | PC端服务器IP地址 | ************* | ************* |
| PC端口 | PC端监听端口 | 8888 | 8888 |
| 自动连接 | 应用启动时自动连接 | 否 | 是/否 |

## 手动配置方法

如果脚本无法使用，可以手动配置:

### 1. 安装APK
```bash
adb install -r app-debug.apk
```

### 2. 创建配置文件
在设备存储中创建 `/sdcard/remote_control_config.txt`:
```
PC_IP=*************
PC_PORT=8888
AUTO_START=false
```

### 3. 推送配置文件
```bash
adb push remote_control_config.txt /sdcard/
```

## 权限配置指南

Android应用需要以下权限才能正常工作:

### 1. 屏幕录制权限
- 应用会自动请求此权限
- 在系统弹窗中点击"立即开始"

### 2. 无障碍服务权限
- 进入: 设置 > 辅助功能 > 已安装的服务
- 找到"Android远程控制"并启用
- 授予权限

### 3. 悬浮窗权限
- 进入: 设置 > 应用 > 特殊应用权限 > 显示在其他应用上层
- 找到"Android远程控制"并启用

## 网络配置

### 局域网连接
1. 确保PC和Android设备在同一WiFi网络
2. PC端显示的IP地址应该可以从Android设备访问
3. 检查防火墙是否允许相应端口

### 防火墙配置
Windows防火墙需要允许应用通过防火墙:
```
控制面板 > 系统和安全 > Windows Defender 防火墙 > 允许应用通过防火墙
```

## 故障排除

### 连接问题
1. **无法连接到PC**
   - 检查IP地址是否正确
   - 检查端口是否被占用
   - 检查防火墙设置

2. **权限问题**
   - 重新授予相关权限
   - 检查无障碍服务是否正常启用

3. **屏幕显示问题**
   - 重启屏幕录制权限
   - 检查设备性能

### 日志查看
```bash
# 查看应用日志
adb logcat | grep "RemoteControl"

# 查看系统日志
adb logcat
```

## 高级配置

### 自定义配置
可以修改应用代码中的默认配置:
- `MainActivity.kt` 中的默认IP和端口
- `RemoteControlService.kt` 中的重连参数
- 屏幕截图质量和频率设置

### 批量部署
使用PowerShell脚本支持批量配置多个设备:
```powershell
# 获取所有连接的设备
adb devices

# 循环配置每个设备
foreach ($device in $devices) {
    adb -s $device install -r app-debug.apk
    # 配置参数...
}
```