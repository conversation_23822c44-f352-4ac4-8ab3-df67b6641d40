package com.remotecontrol.android.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.GestureDescription
import android.content.Context
import android.graphics.Path
import android.os.Build
import android.util.Log
import android.view.KeyEvent
import com.remotecontrol.android.model.TouchCommand
import kotlinx.coroutines.*
import java.lang.RuntimeException

class InputSimulator(private val context: Context) {
    
    companion object {
        private const val TAG = "InputSimulator"
        
        // Android系统按键码
        const val KEYCODE_HOME = 3
        const val KEYCODE_BACK = 4
        const val KEYCODE_MENU = 82
        const val KEYCODE_POWER = 26
        const val KEYCODE_VOLUME_UP = 24
        const val KEYCODE_VOLUME_DOWN = 25
        const val KEYCODE_ENTER = 66
        const val KEYCODE_DELETE = 67
        
        // 静态引用，用于从无障碍服务设置
        private var accessibilityService: AccessibilityService? = null
        
        fun setAccessibilityService(service: AccessibilityService) {
            accessibilityService = service
        }
        
        fun clearAccessibilityService() {
            accessibilityService = null
        }
    }
    
    /**
     * 执行触摸操作
     */
    suspend fun performTouch(touchCommand: TouchCommand) {
        withContext(Dispatchers.Main) {
            try {
                when (touchCommand.action) {
                    "tap" -> performTap(touchCommand.x, touchCommand.y)
                    "long_press" -> performLongPress(touchCommand.x, touchCommand.y, touchCommand.duration)
                    "swipe" -> performSwipe(
                        touchCommand.x, touchCommand.y,
                        touchCommand.endX, touchCommand.endY,
                        touchCommand.duration
                    )
                    else -> Log.w(TAG, "Unknown touch action: ${touchCommand.action}")
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error performing touch: ${e.message}")
            }
        }
    }
    
    /**
     * 执行点击操作
     */
    private fun performTap(x: Int, y: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            accessibilityService?.let { service ->
                val path = Path().apply {
                    moveTo(x.toFloat(), y.toFloat())
                }
                
                val gesture = GestureDescription.Builder()
                    .addStroke(GestureDescription.StrokeDescription(path, 0, 50))
                    .build()
                
                service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
                    override fun onCompleted(gestureDescription: GestureDescription?) {
                        Log.d(TAG, "Tap gesture completed at ($x, $y)")
                    }
                    
                    override fun onCancelled(gestureDescription: GestureDescription?) {
                        Log.w(TAG, "Tap gesture cancelled")
                    }
                }, null)
            } ?: run {
                Log.e(TAG, "AccessibilityService not available for tap")
                tryShellCommand("input tap $x $y")
            }
        } else {
            tryShellCommand("input tap $x $y")
        }
    }
    
    /**
     * 执行长按操作
     */
    private fun performLongPress(x: Int, y: Int, duration: Int) {
        val pressDuration = if (duration > 0) duration.toLong() else 1000L
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            accessibilityService?.let { service ->
                val path = Path().apply {
                    moveTo(x.toFloat(), y.toFloat())
                }
                
                val gesture = GestureDescription.Builder()
                    .addStroke(GestureDescription.StrokeDescription(path, 0, pressDuration))
                    .build()
                
                service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
                    override fun onCompleted(gestureDescription: GestureDescription?) {
                        Log.d(TAG, "Long press gesture completed at ($x, $y)")
                    }
                    
                    override fun onCancelled(gestureDescription: GestureDescription?) {
                        Log.w(TAG, "Long press gesture cancelled")
                    }
                }, null)
            } ?: run {
                Log.e(TAG, "AccessibilityService not available for long press")
                tryShellCommand("input swipe $x $y $x $y $pressDuration")
            }
        } else {
            tryShellCommand("input swipe $x $y $x $y $pressDuration")
        }
    }
    
    /**
     * 执行滑动操作
     */
    private fun performSwipe(startX: Int, startY: Int, endX: Int, endY: Int, duration: Int) {
        val swipeDuration = if (duration > 0) duration.toLong() else 300L
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            accessibilityService?.let { service ->
                val path = Path().apply {
                    moveTo(startX.toFloat(), startY.toFloat())
                    lineTo(endX.toFloat(), endY.toFloat())
                }
                
                val gesture = GestureDescription.Builder()
                    .addStroke(GestureDescription.StrokeDescription(path, 0, swipeDuration))
                    .build()
                
                service.dispatchGesture(gesture, object : AccessibilityService.GestureResultCallback() {
                    override fun onCompleted(gestureDescription: GestureDescription?) {
                        Log.d(TAG, "Swipe gesture completed from ($startX, $startY) to ($endX, $endY)")
                    }
                    
                    override fun onCancelled(gestureDescription: GestureDescription?) {
                        Log.w(TAG, "Swipe gesture cancelled")
                    }
                }, null)
            } ?: run {
                Log.e(TAG, "AccessibilityService not available for swipe")
                tryShellCommand("input swipe $startX $startY $endX $endY $swipeDuration")
            }
        } else {
            tryShellCommand("input swipe $startX $startY $endX $endY $swipeDuration")
        }
    }
    
    /**
     * 执行按键操作
     */
    suspend fun performKeyPress(keyCode: Int) {
        withContext(Dispatchers.Main) {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                    accessibilityService?.let { service ->
                        val success = service.performGlobalAction(getGlobalActionForKeyCode(keyCode))
                        if (success) {
                            Log.d(TAG, "Key press performed: $keyCode")
                            return@withContext
                        }
                    }
                }
                
                // 回退到shell命令
                tryShellCommand("input keyevent $keyCode")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error performing key press: ${e.message}")
            }
        }
    }
    
    /**
     * 输入文本
     */
    suspend fun inputText(text: String) {
        withContext(Dispatchers.IO) {
            try {
                // 转义特殊字符
                val escapedText = text.replace(" ", "%s")
                    .replace("'", "\\'")
                    .replace("\"", "\\\"")
                    .replace("\n", "\\n")
                
                tryShellCommand("input text '$escapedText'")
                Log.d(TAG, "Text input: $text")
                
            } catch (e: Exception) {
                Log.e(TAG, "Error inputting text: ${e.message}")
            }
        }
    }
    
    /**
     * 将按键码转换为全局动作
     */
    private fun getGlobalActionForKeyCode(keyCode: Int): Int {
        return when (keyCode) {
            KEYCODE_HOME -> AccessibilityService.GLOBAL_ACTION_HOME
            KEYCODE_BACK -> AccessibilityService.GLOBAL_ACTION_BACK
            KEYCODE_MENU -> if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
                AccessibilityService.GLOBAL_ACTION_RECENTS
            } else -1
            else -> -1
        }
    }
    
    /**
     * 尝试执行shell命令
     */
    private fun tryShellCommand(command: String) {
        try {
            Log.d(TAG, "Executing shell command: $command")
            val process = Runtime.getRuntime().exec("su -c '$command'")
            val exitCode = process.waitFor()
            
            if (exitCode == 0) {
                Log.d(TAG, "Shell command executed successfully")
            } else {
                // 尝试不使用su权限
                Log.w(TAG, "Shell command with su failed, trying without su")
                val fallbackProcess = Runtime.getRuntime().exec(command)
                val fallbackExitCode = fallbackProcess.waitFor()
                
                if (fallbackExitCode == 0) {
                    Log.d(TAG, "Fallback shell command executed successfully")
                } else {
                    Log.e(TAG, "Shell command failed with exit code: $fallbackExitCode")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error executing shell command: ${e.message}")
        }
    }
    
    /**
     * 检查是否有必要的权限
     */
    fun hasRequiredPermissions(): Boolean {
        return accessibilityService != null
    }
    
    /**
     * 模拟音量键
     */
    suspend fun performVolumeKey(isVolumeUp: Boolean) {
        val keyCode = if (isVolumeUp) KEYCODE_VOLUME_UP else KEYCODE_VOLUME_DOWN
        performKeyPress(keyCode)
    }
    
    /**
     * 模拟电源键
     */
    suspend fun performPowerKey() {
        performKeyPress(KEYCODE_POWER)
    }
}