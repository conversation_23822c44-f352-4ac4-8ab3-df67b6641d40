@echo off
chcp 65001 >nul
title Android远程控制APK构建工具

echo.
echo ==========================================
echo    Android远程控制APK构建工具
echo ==========================================
echo.

:: 检查Gradle环境
where gradle >nul 2>nul
if %errorlevel% neq 0 (
    echo [错误] 未找到 Gradle，正在尝试使用项目自带的Gradle Wrapper...
    if exist gradlew.bat (
        set GRADLE_CMD=gradlew.bat
    ) else (
        echo [错误] 未找到 gradlew.bat，请确保:
        echo 1. 已安装Gradle并添加到PATH
        echo 2. 或者项目包含Gradle Wrapper
        pause
        exit /b 1
    )
) else (
    set GRADLE_CMD=gradle
)

:: 检查Android SDK
if "%ANDROID_SDK_ROOT%"=="" if "%ANDROID_HOME%"=="" (
    echo [警告] 未设置Android SDK环境变量
    echo 请设置 ANDROID_SDK_ROOT 或 ANDROID_HOME 环境变量
    echo.
)

echo [信息] 开始构建APK...
echo.

:: 清理项目
echo [1/4] 清理项目...
%GRADLE_CMD% clean
if %errorlevel% neq 0 (
    echo [错误] 项目清理失败
    pause
    exit /b 1
)

:: 构建Debug版本APK
echo.
echo [2/4] 构建Debug APK...
%GRADLE_CMD% assembleDebug
if %errorlevel% neq 0 (
    echo [错误] APK构建失败
    pause
    exit /b 1
)

:: 检查输出文件
set APK_PATH=app\build\outputs\apk\debug\app-debug.apk
if not exist "%APK_PATH%" (
    echo [错误] 未找到构建的APK文件: %APK_PATH%
    pause
    exit /b 1
)

echo.
echo [3/4] APK构建成功!
echo 文件位置: %APK_PATH%

:: 获取APK信息
echo.
echo [4/4] 获取APK信息...
if exist "%ANDROID_SDK_ROOT%\build-tools" (
    for /f %%i in ('dir /b "%ANDROID_SDK_ROOT%\build-tools" ^| sort /r') do (
        set BUILD_TOOLS_VERSION=%%i
        goto found_build_tools
    )
)

:found_build_tools
if defined BUILD_TOOLS_VERSION (
    set AAPT_PATH=%ANDROID_SDK_ROOT%\build-tools\%BUILD_TOOLS_VERSION%\aapt.exe
    if exist "%AAPT_PATH%" (
        echo APK详细信息:
        "%AAPT_PATH%" dump badging "%APK_PATH%" | findstr "package:"
        "%AAPT_PATH%" dump badging "%APK_PATH%" | findstr "versionCode"
        "%AAPT_PATH%" dump badging "%APK_PATH%" | findstr "versionName"
        "%AAPT_PATH%" dump badging "%APK_PATH%" | findstr "minSdkVersion"
        "%AAPT_PATH%" dump badging "%APK_PATH%" | findstr "targetSdkVersion"
    )
)

:: 计算文件大小
for %%A in ("%APK_PATH%") do (
    set APK_SIZE=%%~zA
)

echo.
echo APK文件大小: %APK_SIZE% 字节
echo.

echo ==========================================
echo 构建完成!
echo ==========================================
echo.
echo 下一步操作:
echo 1. 运行 configure_apk.bat 配置并安装APK
echo 2. 或手动安装: adb install -r "%APK_PATH%"
echo 3. 或直接复制APK文件到设备安装
echo.

set /p RUN_CONFIG="是否立即运行配置脚本? (Y/n): "
if /i not "%RUN_CONFIG%"=="n" (
    if exist configure_apk.bat (
        echo.
        echo 正在启动配置脚本...
        call configure_apk.bat
    ) else (
        echo [警告] 未找到 configure_apk.bat 脚本
    )
)

echo.
echo 按任意键退出...
pause >nul