/*
 * Copyright 2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.plugins.ide.internal.tooling.eclipse;

import java.io.Serializable;

public class DefaultEclipseLinkedResource implements Serializable {

    private String name;
    private String type;
    private String location;
    private String locationUri;

    public DefaultEclipseLinkedResource(String name, String type, String location, String locationUri) {
        this.name = name;
        this.type = type;
        this.location = location;
        this.locationUri = locationUri;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public String getLocation() {
        return location;
    }

    public String getLocationUri() {
        return locationUri;
    }
}
