# Android远程控制APK配置工具 (PowerShell版本)
# 支持APK重新打包和配置注入

param(
    [string]$IPAddress = "",
    [int]$Port = 8888,
    [switch]$AutoStart = $false,
    [switch]$InstallOnly = $false,
    [switch]$Help = $false
)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Show-Help {
    Write-Host @"
Android远程控制APK配置工具

用法: .\Configure-APK.ps1 [参数]

参数:
  -IPAddress <IP>     PC端IP地址 (默认: 交互式输入)
  -Port <端口>        PC端端口 (默认: 8888)
  -AutoStart          启用自动连接 (默认: 禁用)
  -InstallOnly        仅安装APK，不配置参数
  -Help               显示此帮助信息

示例:
  .\Configure-APK.ps1 -IPAddress "*************" -Port 8888 -AutoStart
  .\Configure-APK.ps1 -InstallOnly

注意:
  - 需要安装Android SDK (aapt, adb工具)
  - Android设备需要启用USB调试
  - 首次运行需要授权USB调试
"@
}

function Test-AndroidSDK {
    $aaptPath = Get-Command "aapt" -ErrorAction SilentlyContinue
    $adbPath = Get-Command "adb" -ErrorAction SilentlyContinue
    
    if (-not $aaptPath) {
        Write-Error "未找到 aapt 工具。请安装Android SDK并将build-tools目录添加到PATH"
        return $false
    }
    
    if (-not $adbPath) {
        Write-Error "未找到 adb 工具。请安装Android SDK并将platform-tools目录添加到PATH"
        return $false
    }
    
    return $true
}

function Test-IPAddress {
    param([string]$IP)
    
    try {
        $null = [System.Net.IPAddress]::Parse($IP)
        return $true
    }
    catch {
        return $false
    }
}

function Get-AndroidDevices {
    $devices = adb devices | Where-Object { $_ -match "\tdevice$" }
    return $devices.Count
}

function Install-APK {
    param([string]$APKPath)
    
    Write-Host "正在安装APK..." -ForegroundColor Yellow
    
    $result = adb install -r $APKPath 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ APK安装成功" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ APK安装失败: $result" -ForegroundColor Red
        return $false
    }
}

function Set-AppConfiguration {
    param(
        [string]$IP,
        [int]$Port,
        [bool]$AutoStart
    )
    
    Write-Host "正在配置应用参数..." -ForegroundColor Yellow
    
    # 创建配置文件
    $configContent = @"
PC_IP=$IP
PC_PORT=$Port
AUTO_START=$($AutoStart.ToString().ToLower())
"@
    
    # 将配置推送到设备
    $configFile = Join-Path $env:TEMP "remote_control_config.txt"
    $configContent | Out-File -FilePath $configFile -Encoding UTF8
    
    $result = adb push $configFile "/sdcard/remote_control_config.txt" 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 配置文件已推送到设备" -ForegroundColor Green
    } else {
        Write-Host "⚠ 配置文件推送失败: $result" -ForegroundColor Yellow
    }
    
    # 清理临时文件
    Remove-Item $configFile -ErrorAction SilentlyContinue
    
    # 尝试启动应用
    Write-Host "正在启动应用..." -ForegroundColor Yellow
    adb shell am start -n "com.remotecontrol.android/.MainActivity" | Out-Null
    
    Start-Sleep -Seconds 2
    Write-Host "✓ 应用已启动，请在设备上完成权限配置" -ForegroundColor Green
}

function Show-Instructions {
    param(
        [string]$IP,
        [int]$Port
    )
    
    Write-Host @"

==========================================
配置完成! 
==========================================

配置信息:
  PC IP地址: $IP
  PC端口:    $Port
  
使用说明:
  1. 在Android设备上会自动打开应用
  2. 请依次授予以下权限:
     - 屏幕录制权限
     - 无障碍服务权限  
     - 悬浮窗权限
  3. 确认IP和端口设置正确
  4. 点击"连接"按钮

注意事项:
  - 确保PC端已启动服务
  - 确保设备和PC在同一网络
  - 如连接失败请检查防火墙设置

"@ -ForegroundColor Cyan
}

# 主程序开始
if ($Help) {
    Show-Help
    exit 0
}

Write-Host @"
==========================================
  Android远程控制APK配置工具 (PowerShell)
==========================================
"@ -ForegroundColor Cyan

# 检查Android SDK
if (-not (Test-AndroidSDK)) {
    exit 1
}

# 检查APK文件
$apkPath = Join-Path $PSScriptRoot "app\build\outputs\apk\debug\app-debug.apk"
if (-not (Test-Path $apkPath)) {
    Write-Error "未找到APK文件: $apkPath"
    Write-Host "请先编译Android项目生成APK文件" -ForegroundColor Yellow
    exit 1
}

Write-Host "✓ 找到APK文件: $apkPath" -ForegroundColor Green

# 检查设备连接
$deviceCount = Get-AndroidDevices
if ($deviceCount -eq 0) {
    Write-Warning "未检测到连接的Android设备"
    Write-Host @"
请确保:
  1. Android设备已通过USB连接
  2. 已启用开发者选项和USB调试
  3. 已授权此电脑的USB调试权限
"@ -ForegroundColor Yellow
    
    $continue = Read-Host "是否继续? (y/N)"
    if ($continue -ne "y" -and $continue -ne "Y") {
        exit 0
    }
} else {
    Write-Host "✓ 检测到 $deviceCount 个连接的设备" -ForegroundColor Green
}

# 如果只是安装APK
if ($InstallOnly) {
    if (Install-APK $apkPath) {
        Write-Host "APK安装完成。请手动配置连接参数。" -ForegroundColor Green
    }
    exit 0
}

# 获取配置参数
if (-not $IPAddress) {
    do {
        $IPAddress = Read-Host "请输入PC端IP地址 (默认: *************)"
        if (-not $IPAddress) { $IPAddress = "*************" }
        
        if (-not (Test-IPAddress $IPAddress)) {
            Write-Host "IP地址格式无效，请重新输入" -ForegroundColor Red
            $IPAddress = ""
        }
    } while (-not $IPAddress)
}

if ($Port -lt 1 -or $Port -gt 65535) {
    do {
        $portInput = Read-Host "请输入PC端端口 (1-65535, 默认: 8888)"
        if (-not $portInput) { $Port = 8888 }
        else { 
            try { $Port = [int]$portInput }
            catch { $Port = 0 }
        }
        
        if ($Port -lt 1 -or $Port -gt 65535) {
            Write-Host "端口范围无效，请输入1-65535之间的数字" -ForegroundColor Red
        }
    } while ($Port -lt 1 -or $Port -gt 65535)
}

if (-not $AutoStart.IsPresent) {
    $autoStartInput = Read-Host "是否启用自动连接? (y/N)"
    $AutoStart = ($autoStartInput -eq "y" -or $autoStartInput -eq "Y")
}

# 确认配置
Write-Host @"

==========================================
配置信息确认:
  PC IP地址: $IPAddress
  PC端口:    $Port
  自动连接:  $AutoStart
==========================================
"@ -ForegroundColor Yellow

$confirm = Read-Host "确认配置并安装? (Y/n)"
if ($confirm -eq "n" -or $confirm -eq "N") {
    Write-Host "已取消操作" -ForegroundColor Yellow
    exit 0
}

# 安装APK
if (-not (Install-APK $apkPath)) {
    exit 1
}

# 配置应用
Set-AppConfiguration -IP $IPAddress -Port $Port -AutoStart $AutoStart

# 显示说明
Show-Instructions -IP $IPAddress -Port $Port

Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")