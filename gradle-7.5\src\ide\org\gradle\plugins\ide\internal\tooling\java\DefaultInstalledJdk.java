/*
 * Copyright 2015 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.plugins.ide.internal.tooling.java;

import org.gradle.api.JavaVersion;
import org.gradle.internal.jvm.Jvm;

import java.io.File;
import java.io.Serializable;

public class DefaultInstalledJdk implements Serializable {

    private final File javaHome;
    private final JavaVersion javaVersion;

    public static DefaultInstalledJdk current() {
        Jvm current = Jvm.current();
        return new DefaultInstalledJdk(current.getJavaHome(), current.getJavaVersion());
    }

    public DefaultInstalledJdk(File javaHome, JavaVersion javaVersion) {
        this.javaHome = javaHome;
        this.javaVersion = javaVersion;
    }

    public JavaVersion getJavaVersion() {
        return javaVersion;
    }

    public File getJavaHome() {
        return javaHome;
    }
}
