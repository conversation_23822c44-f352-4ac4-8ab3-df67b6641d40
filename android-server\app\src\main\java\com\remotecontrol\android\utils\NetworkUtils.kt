package com.remotecontrol.android.utils

import java.net.InetAddress
import java.net.NetworkInterface
import java.util.*

object NetworkUtils {
    
    /**
     * 获取本机IP地址
     */
    fun getLocalIPAddress(): String {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                
                // 跳过回环接口和非活动接口
                if (networkInterface.isLoopback || !networkInterface.isUp) {
                    continue
                }
                
                val addresses = networkInterface.inetAddresses
                while (addresses.hasMoreElements()) {
                    val address = addresses.nextElement()
                    
                    // 只返回IPv4地址，且不是回环地址
                    if (!address.isLoopbackAddress && 
                        !address.isLinkLocalAddress && 
                        address.hostAddress?.contains(":") == false) {
                        return address.hostAddress ?: "未知"
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return "未知"
    }
    
    /**
     * 获取WiFi IP地址
     */
    fun getWifiIPAddress(): String {
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                
                // 查找WiFi接口（通常名称包含wlan）
                if (networkInterface.name.contains("wlan", ignoreCase = true) && 
                    networkInterface.isUp) {
                    
                    val addresses = networkInterface.inetAddresses
                    while (addresses.hasMoreElements()) {
                        val address = addresses.nextElement()
                        
                        if (!address.isLoopbackAddress && 
                            !address.isLinkLocalAddress && 
                            address.hostAddress?.contains(":") == false) {
                            return address.hostAddress ?: "未知"
                        }
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return getLocalIPAddress() // 回退到通用方法
    }
    
    /**
     * 检查IP地址格式是否有效
     */
    fun isValidIPAddress(ip: String): Boolean {
        return try {
            val parts = ip.split(".")
            if (parts.size != 4) return false
            
            parts.all { part ->
                val num = part.toIntOrNull()
                num != null && num in 0..255
            }
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 检查端口号是否有效
     */
    fun isValidPort(port: String): Boolean {
        return try {
            val portNum = port.toInt()
            portNum in 1..65535
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * 获取所有网络接口信息
     */
    fun getAllNetworkInterfaces(): List<NetworkInterfaceInfo> {
        val interfaceList = mutableListOf<NetworkInterfaceInfo>()
        
        try {
            val interfaces = NetworkInterface.getNetworkInterfaces()
            while (interfaces.hasMoreElements()) {
                val networkInterface = interfaces.nextElement()
                
                if (!networkInterface.isUp) continue
                
                val addresses = mutableListOf<String>()
                val inetAddresses = networkInterface.inetAddresses
                while (inetAddresses.hasMoreElements()) {
                    val address = inetAddresses.nextElement()
                    if (address.hostAddress?.contains(":") == false) {
                        addresses.add(address.hostAddress ?: "")
                    }
                }
                
                if (addresses.isNotEmpty()) {
                    interfaceList.add(
                        NetworkInterfaceInfo(
                            name = networkInterface.name,
                            displayName = networkInterface.displayName ?: networkInterface.name,
                            addresses = addresses,
                            isLoopback = networkInterface.isLoopback
                        )
                    )
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        
        return interfaceList
    }
    
    data class NetworkInterfaceInfo(
        val name: String,
        val displayName: String,
        val addresses: List<String>,
        val isLoopback: Boolean
    )
}