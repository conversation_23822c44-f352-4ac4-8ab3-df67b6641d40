<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="6cb540f2-d966-4158-9f41-0a50afab93b9" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ClangdSettings">
    <option name="formatViaClangd" value="false" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 2
}]]></component>
  <component name="ProjectId" id="30xRRqiBpg5eYu6u9wU8CPtdszC" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.cidr.known.project.marker": "true",
    "RunOnceActivity.readMode.enableVisualFormatting": "true",
    "cf.first.check.clang-format": "false",
    "cidr.known.project.marker": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/新建文件夹 (13)/android-server/gradle",
    "settings.editor.selected.configurable": "AndroidSdkUpdater"
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="6cb540f2-d966-4158-9f41-0a50afab93b9" name="更改" comment="" />
      <created>1754567231775</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754567231775</updated>
    </task>
    <servers />
  </component>
</project>