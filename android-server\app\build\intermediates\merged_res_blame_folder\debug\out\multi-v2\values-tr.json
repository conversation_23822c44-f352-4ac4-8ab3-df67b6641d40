{"logs": [{"outputFile": "com.remotecontrol.android.app-mergeDebugResources-36:/values-tr/values-tr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10dc035393e004282fd64c0079b394e4\\transformed\\material-1.11.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,340,415,492,591,682,778,890,972,1036,1127,1204,1265,1356,1419,1482,1541,1610,1673,1727,1835,1893,1955,2009,2082,2203,2287,2378,2518,2595,2671,2802,2889,2965,3018,3072,3138,3208,3285,3368,3448,3519,3594,3672,3743,3844,3929,4018,4113,4206,4278,4350,4446,4498,4584,4651,4735,4825,4887,4951,5014,5084,5178,5280,5369,5469,5526,5584", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "260,335,410,487,586,677,773,885,967,1031,1122,1199,1260,1351,1414,1477,1536,1605,1668,1722,1830,1888,1950,2004,2077,2198,2282,2373,2513,2590,2666,2797,2884,2960,3013,3067,3133,3203,3280,3363,3443,3514,3589,3667,3738,3839,3924,4013,4108,4201,4273,4345,4441,4493,4579,4646,4730,4820,4882,4946,5009,5079,5173,5275,5364,5464,5521,5579,5658"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3007,3082,3157,3234,3333,4137,4233,4345,4427,4491,4582,4659,4720,4811,4874,4937,4996,5065,5128,5182,5290,5348,5410,5464,5537,5658,5742,5833,5973,6050,6126,6257,6344,6420,6473,6527,6593,6663,6740,6823,6903,6974,7049,7127,7198,7299,7384,7473,7568,7661,7733,7805,7901,7953,8039,8106,8190,8280,8342,8406,8469,8539,8633,8735,8824,8924,8981,9039", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,74,74,76,98,90,95,111,81,63,90,76,60,90,62,62,58,68,62,53,107,57,61,53,72,120,83,90,139,76,75,130,86,75,52,53,65,69,76,82,79,70,74,77,70,100,84,88,94,92,71,71,95,51,85,66,83,89,61,63,62,69,93,101,88,99,56,57,78", "endOffsets": "310,3077,3152,3229,3328,3419,4228,4340,4422,4486,4577,4654,4715,4806,4869,4932,4991,5060,5123,5177,5285,5343,5405,5459,5532,5653,5737,5828,5968,6045,6121,6252,6339,6415,6468,6522,6588,6658,6735,6818,6898,6969,7044,7122,7193,7294,7379,7468,7563,7656,7728,7800,7896,7948,8034,8101,8185,8275,8337,8401,8464,8534,8628,8730,8819,8919,8976,9034,9113"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c6dc7adb4bd25c9f6fecda703177518b\\transformed\\core-1.12.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3424,3521,3623,3721,3818,3920,4026,9198", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "3516,3618,3716,3813,3915,4021,4132,9294"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c5bc1c461f48fbd1a3b03cc22fccf5ce\\transformed\\appcompat-1.6.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,318,430,515,621,741,821,896,987,1080,1172,1266,1366,1459,1561,1656,1747,1838,1917,2024,2128,2224,2331,2434,2543,2699,2797", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "214,313,425,510,616,736,816,891,982,1075,1167,1261,1361,1454,1556,1651,1742,1833,1912,2019,2123,2219,2326,2429,2538,2694,2792,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,429,528,640,725,831,951,1031,1106,1197,1290,1382,1476,1576,1669,1771,1866,1957,2048,2127,2234,2338,2434,2541,2644,2753,2909,9118", "endColumns": "113,98,111,84,105,119,79,74,90,92,91,93,99,92,101,94,90,90,78,106,103,95,106,102,108,155,97,79", "endOffsets": "424,523,635,720,826,946,1026,1101,1192,1285,1377,1471,1571,1664,1766,1861,1952,2043,2122,2229,2333,2429,2536,2639,2748,2904,3002,9193"}}]}]}