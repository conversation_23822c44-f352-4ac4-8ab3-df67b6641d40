<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main" modulePackage="com.remotecontrol.android" filePath="app\src\main\res\layout\activity_main.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_main_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="238" endOffset="12"/></Target><Target id="@+id/txtServiceStatus" view="TextView"><Expressions/><location startLine="46" startOffset="16" endLine="53" endOffset="56"/></Target><Target id="@+id/btnStartService" view="Button"><Expressions/><location startLine="60" startOffset="20" endLine="66" endOffset="56"/></Target><Target id="@+id/btnStopService" view="Button"><Expressions/><location startLine="68" startOffset="20" endLine="75" endOffset="58"/></Target><Target id="@+id/txtIpAddress" view="TextView"><Expressions/><location startLine="118" startOffset="20" endLine="124" endOffset="50"/></Target><Target id="@+id/txtPort" view="TextView"><Expressions/><location startLine="140" startOffset="20" endLine="146" endOffset="50"/></Target><Target id="@+id/txtConnectedClients" view="TextView"><Expressions/><location startLine="176" startOffset="16" endLine="181" endOffset="45"/></Target><Target id="@+id/btnAccessibilityPermission" view="Button"><Expressions/><location startLine="209" startOffset="16" endLine="215" endOffset="75"/></Target><Target id="@+id/btnMediaProjectionPermission" view="Button"><Expressions/><location startLine="217" startOffset="16" endLine="223" endOffset="75"/></Target><Target id="@+id/btnSystemAlertPermission" view="Button"><Expressions/><location startLine="225" startOffset="16" endLine="230" endOffset="75"/></Target></Targets></Layout>