package com.remotecontrol.android.model

import com.google.gson.annotations.SerializedName

/**
 * 远程通信消息基类
 */
data class RemoteMessage(
    @SerializedName("type")
    val type: String,
    
    @SerializedName("timestamp")
    val timestamp: Long = System.currentTimeMillis(),
    
    @SerializedName("data")
    val data: Any? = null
)

/**
 * 消息类型常量
 */
object MessageType {
    // 认证相关
    const val AUTH_REQUEST = "AUTH_REQUEST"
    const val AUTH_RESPONSE = "AUTH_RESPONSE"
    
    // 屏幕数据
    const val SCREEN_DATA = "SCREEN_DATA"
    const val SCREEN_REQUEST = "SCREEN_REQUEST"
    const val SCREEN_RESPONSE = "SCREEN_RESPONSE"
    
    // 控制指令
    const val TOUCH_COMMAND = "TOUCH_COMMAND"
    const val KEY_COMMAND = "KEY_COMMAND"
    const val TEXT_COMMAND = "TEXT_COMMAND"
    
    // 系统消息
    const val HEARTBEAT = "HEARTBEAT"
    const val ERROR = "ERROR"
}

/**
 * 设备信息
 */
data class DeviceInfo(
    @SerializedName("model")
    val model: String,
    
    @SerializedName("android_version")
    val androidVersion: String,
    
    @SerializedName("screen_width")
    val screenWidth: Int,
    
    @SerializedName("screen_height")
    val screenHeight: Int,
    
    @SerializedName("density")
    val density: Float
)

/**
 * 认证请求数据
 */
data class AuthRequestData(
    @SerializedName("device_info")
    val deviceInfo: DeviceInfo,
    
    @SerializedName("version")
    val version: String = "1.0.0"
)

/**
 * 认证响应数据
 */
data class AuthResponseData(
    @SerializedName("status")
    val status: String,
    
    @SerializedName("pc_info")
    val pcInfo: PCInfo?
)

data class PCInfo(
    @SerializedName("name")
    val name: String,
    
    @SerializedName("version")
    val version: String
)

/**
 * 屏幕数据
 */
data class ScreenData(
    @SerializedName("image_base64")
    val imageBase64: String,
    
    @SerializedName("width")
    val width: Int,
    
    @SerializedName("height")
    val height: Int,
    
    @SerializedName("quality")
    val quality: Int
)

/**
 * 触摸指令
 */
data class TouchCommand(
    @SerializedName("action")
    val action: String, // tap, long_press, swipe
    
    @SerializedName("x")
    val x: Int,
    
    @SerializedName("y")
    val y: Int,
    
    @SerializedName("duration")
    val duration: Int = 0,
    
    @SerializedName("end_x")
    val endX: Int = 0,
    
    @SerializedName("end_y")
    val endY: Int = 0
)

/**
 * 按键指令
 */
data class KeyCommand(
    @SerializedName("key_code")
    val keyCode: Int,
    
    @SerializedName("action")
    val action: String = "press"
)

/**
 * 文本指令
 */
data class TextCommand(
    @SerializedName("text")
    val text: String
)