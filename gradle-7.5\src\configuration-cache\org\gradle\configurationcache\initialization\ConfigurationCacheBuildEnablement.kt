/*
 * Copyright 2020 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.configurationcache.initialization

import org.gradle.api.internal.SettingsInternal.BUILD_SRC
import org.gradle.internal.build.PublicBuildPath
import org.gradle.util.Path


class ConfigurationCacheBuildEnablement(
    private val buildPath: PublicBuildPath
) {
    val isProblemListenerEnabledForCurrentBuild by lazy {
        BUILD_SRC != buildPath.buildPath.lastSegment()
    }

    private
    fun Path.lastSegment(): String? =
        segmentCount()
            .takeIf { it > 1 }
            ?.let { segment(it - 1) }
}
