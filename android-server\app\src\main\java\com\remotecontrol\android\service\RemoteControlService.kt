package com.remotecontrol.android.service

import android.app.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.IBinder
import android.util.Log
import androidx.core.app.NotificationCompat
import com.google.gson.Gson
import com.remotecontrol.android.R
import com.remotecontrol.android.MainActivity
import com.remotecontrol.android.model.*
import com.remotecontrol.android.utils.NetworkUtils
import kotlinx.coroutines.*
import java.io.*
import java.net.Socket
import java.util.concurrent.atomic.AtomicBoolean

class RemoteControlService : Service() {
    
    companion object {
        private const val TAG = "RemoteControlService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "remote_control_channel"
        
        // 服务动作
        const val ACTION_START = "com.remotecontrol.android.START"
        const val ACTION_STOP = "com.remotecontrol.android.STOP"
        const val ACTION_CONNECT = "com.remotecontrol.android.CONNECT"
        const val ACTION_DISCONNECT = "com.remotecontrol.android.DISCONNECT"
        
        // 静态变量用于权限检查
        private var mediaProjectionResultCode: Int = 0
        private var mediaProjectionData: Intent? = null
        private val serviceRunning = AtomicBoolean(false)
        
        fun setMediaProjectionData(resultCode: Int, data: Intent) {
            mediaProjectionResultCode = resultCode
            mediaProjectionData = data
        }
        
        fun hasMediaProjectionData(): Boolean {
            return mediaProjectionData != null && mediaProjectionResultCode != 0
        }
        
        fun isServiceRunning(): Boolean {
            return serviceRunning.get()
        }
    }
    
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    private var socket: Socket? = null
    private var outputStream: BufferedWriter? = null
    private var inputStream: BufferedReader? = null
    private var isConnected = AtomicBoolean(false)
    private var reconnectAttempts = 0
    private val maxReconnectAttempts = 5
    
    private lateinit var screenCaptureManager: ScreenCaptureManager
    private lateinit var inputSimulator: InputSimulator
    private val gson = Gson()
    
    // 权限响应广播接收器
    private val permissionResponseReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                "com.remotecontrol.android.SCREEN_PERMISSION_GRANTED" -> {
                    handleScreenPermissionGranted()
                }
                "com.remotecontrol.android.SCREEN_PERMISSION_DENIED" -> {
                    handleScreenPermissionDenied()
                }
            }
        }
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        screenCaptureManager = ScreenCaptureManager(this)
        inputSimulator = InputSimulator(this)
        serviceRunning.set(true)
        
        // 注册权限响应广播接收器
        val filter = IntentFilter().apply {
            addAction("com.remotecontrol.android.SCREEN_PERMISSION_GRANTED")
            addAction("com.remotecontrol.android.SCREEN_PERMISSION_DENIED")
        }
        registerReceiver(permissionResponseReceiver, filter)
        
        Log.d(TAG, "RemoteControlService created")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START -> {
                Log.d(TAG, "Starting service without connection")
                startForegroundService()
                updateNotification("服务已启动，可以手动连接")
            }
            ACTION_CONNECT -> {
                val pcIP = intent.getStringExtra("pc_ip") ?: return START_NOT_STICKY
                val pcPort = intent.getIntExtra("pc_port", 8888)
                startForegroundService()
                connectToPC(pcIP, pcPort)
            }
            ACTION_DISCONNECT, ACTION_STOP -> {
                disconnectFromPC()
                stopSelf()
            }
        }
        // 返回START_STICKY确保服务被系统杀死后能自动重启
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
        disconnectFromPC()
        screenCaptureManager.stop()
        serviceRunning.set(false)
        
        // 注销权限响应广播接收器
        try {
            unregisterReceiver(permissionResponseReceiver)
        } catch (e: Exception) {
            Log.w(TAG, "Error unregistering receiver: ${e.message}")
        }
        
        Log.d(TAG, "RemoteControlService destroyed")
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "远程控制服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Android远程控制服务通知"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun startForegroundService() {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, notificationIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Android远程控制")
            .setContentText("正在运行远程控制服务")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()
        
        startForeground(NOTIFICATION_ID, notification)
    }
    
    private fun connectToPC(pcIP: String, pcPort: Int) {
        serviceScope.launch {
            try {
                Log.d(TAG, "Attempting to connect to $pcIP:$pcPort")
                
                // 创建Socket连接
                socket = Socket(pcIP, pcPort).apply {
                    keepAlive = true
                    tcpNoDelay = true
                    soTimeout = 15000  // 15秒读取超时（统一配置）
                    receiveBufferSize = 32768  // 32KB接收缓冲区（增大）
                    sendBufferSize = 32768     // 32KB发送缓冲区（增大）
                    reuseAddress = true        // 重用地址
                    setSoLinger(true, 0)       // 延迟关闭
                    trafficClass = 0x08        // 设置低延迟标志
                }
                outputStream = BufferedWriter(OutputStreamWriter(socket!!.getOutputStream(), "UTF-8"), 16384)  // 16KB缓冲区
                inputStream = BufferedReader(InputStreamReader(socket!!.getInputStream(), "UTF-8"), 16384)   // 16KB缓冲区
                
                isConnected.set(true)
                reconnectAttempts = 0
                
                Log.d(TAG, "Connected to PC successfully")
                updateNotification("已连接到PC")
                
                // 发送认证请求
                sendAuthRequest()
                
                // 启动消息监听
                listenForMessages()
                
                // 启动心跳机制
                startHeartbeat()
                
            } catch (e: Exception) {
                Log.e(TAG, "Connection failed to $pcIP:$pcPort - ${e.message}")
                Log.e(TAG, "Exception type: ${e.javaClass.simpleName}")
                e.printStackTrace()
                updateNotification("连接失败: ${e.message}")
                handleConnectionError(pcIP, pcPort)
            }
        }
    }
    
    private fun disconnectFromPC() {
        isConnected.set(false)
        
        try {
            outputStream?.close()
            inputStream?.close()
            socket?.close()
        } catch (e: Exception) {
            Log.e(TAG, "Error closing connection: ${e.message}")
        }
        
        outputStream = null
        inputStream = null
        socket = null
        
        screenCaptureManager.stop()
        updateNotification("连接已断开")
        Log.d(TAG, "Disconnected from PC")
    }
    
    private suspend fun sendAuthRequest() {
        val deviceInfo = DeviceInfo(
            model = Build.MODEL,
            androidVersion = Build.VERSION.RELEASE,
            screenWidth = screenCaptureManager.getScreenWidth(),
            screenHeight = screenCaptureManager.getScreenHeight(),
            density = resources.displayMetrics.density
        )
        
        val authData = AuthRequestData(deviceInfo)
        val message = RemoteMessage(MessageType.AUTH_REQUEST, data = authData)
        
        sendMessage(message)
    }
    
    private suspend fun listenForMessages() {
        var lastHeartbeatTime = System.currentTimeMillis()
        val heartbeatTimeout = 45000L // 45秒心跳超时（更宽松）
        
        try {
            Log.d(TAG, "开始监听PC端消息")
            updateNotification("正在监听PC端消息...")
            
            while (isConnected.get() && socket?.isConnected == true && !socket!!.isClosed) {
                val line = withContext(Dispatchers.IO) {
                    try {
                        socket?.soTimeout = 15000  // 15秒读取超时（更宽松）
                        inputStream?.readLine()
                    } catch (e: java.net.SocketTimeoutException) {
                        // 检查心跳超时
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastHeartbeatTime > heartbeatTimeout) {
                            Log.w(TAG, "Heart beat timeout detected, attempting recovery")
                            // 尝试发送心跳而不是直接断开
                            try {
                                sendMessage(RemoteMessage(MessageType.HEARTBEAT))
                                lastHeartbeatTime = currentTime
                                Log.d(TAG, "Recovery heartbeat sent")
                            } catch (heartbeatEx: Exception) {
                                Log.e(TAG, "Failed to send recovery heartbeat: ${heartbeatEx.message}")
                                throw Exception("Heart beat timeout - recovery failed")
                            }
                        }
                        null // 继续循环
                    }
                }
                
                if (line != null && line.isNotEmpty()) {
                    Log.d(TAG, "收到消息: ${line.take(100)}")
                    processMessage(line)
                    lastHeartbeatTime = System.currentTimeMillis()
                } else if (line == null) {
                    Log.w(TAG, "Connection closed by server or network error")
                    // 检查socket状态
                    if (socket?.isClosed == true || socket?.isConnected == false) {
                        Log.w(TAG, "Socket is actually closed")
                        break
                    }
                    // 可能是暂时的网络问题，继续尝试
                    delay(1000L)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error reading messages: ${e.message}")
            e.printStackTrace()
            // 区分不同类型的异常
            when (e) {
                is java.net.SocketException -> {
                    Log.w(TAG, "Socket exception: network connection lost")
                }
                is java.io.IOException -> {
                    Log.w(TAG, "IO exception: communication error")
                }
                else -> {
                    Log.e(TAG, "Unexpected exception: ${e.javaClass.simpleName}")
                }
            }
        } finally {
            if (isConnected.get()) {
                Log.w(TAG, "Connection lost unexpectedly, attempting graceful cleanup")
                handleUnexpectedDisconnection()
            }
        }
    }
    
    private suspend fun processMessage(messageJson: String) {
        try {
            val message = gson.fromJson(messageJson, RemoteMessage::class.java)
            
            when (message.type) {
                MessageType.AUTH_RESPONSE -> {
                    handleAuthResponse(message)
                }
                MessageType.SCREEN_REQUEST -> {
                    handleScreenRequest(message)
                }
                MessageType.TOUCH_COMMAND -> {
                    handleTouchCommand(message)
                }
                MessageType.KEY_COMMAND -> {
                    handleKeyCommand(message)
                }
                MessageType.TEXT_COMMAND -> {
                    handleTextCommand(message)
                }
                MessageType.HEARTBEAT -> {
                    // 回复心跳
                    sendMessage(RemoteMessage(MessageType.HEARTBEAT))
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing message: ${e.message}")
        }
    }
    
    private suspend fun handleAuthResponse(message: RemoteMessage) {
        try {
            val authResponse = gson.fromJson(
                gson.toJson(message.data), 
                AuthResponseData::class.java
            )
            
            if (authResponse.status == "success") {
                Log.d(TAG, "Authentication successful")
                updateNotification("认证成功，开始屏幕传输")
                
                // 启动屏幕捕获
                if (hasMediaProjectionData()) {
                    screenCaptureManager.start(mediaProjectionResultCode, mediaProjectionData!!) { imageData ->
                        // 发送屏幕数据
                        serviceScope.launch {
                            sendScreenData(imageData)
                        }
                    }
                } else {
                    Log.e(TAG, "No media projection data available")
                    updateNotification("缺少屏幕录制权限")
                }
            } else {
                Log.e(TAG, "Authentication failed")
                updateNotification("认证失败")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling auth response: ${e.message}")
        }
    }
    
    private suspend fun handleScreenRequest(message: RemoteMessage) {
        try {
            Log.d(TAG, "PC端请求屏幕控制权限")
            
            if (hasMediaProjectionData()) {
                // 已有权限，直接开始屏幕传输
                Log.d(TAG, "Screen permission already granted, starting screen capture")
                sendMessage(RemoteMessage(MessageType.SCREEN_RESPONSE, data = "granted"))
                updateNotification("已开始屏幕传输")
                
                screenCaptureManager.start(mediaProjectionResultCode, mediaProjectionData!!) { imageData ->
                    serviceScope.launch {
                        sendScreenData(imageData)
                    }
                }
            } else {
                // 需要申请权限，发送权限请求广播
                Log.d(TAG, "Screen permission required, requesting from user")
                sendMessage(RemoteMessage(MessageType.SCREEN_RESPONSE, data = "permission_needed"))
                updateNotification("需要屏幕录制权限，请在主界面授权")
                requestScreenPermissionFromUser()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error handling screen request: ${e.message}")
            sendMessage(RemoteMessage(MessageType.SCREEN_RESPONSE, data = "error"))
        }
    }
    
    private fun requestScreenPermissionFromUser() {
        // 发送广播通知MainActivity申请屏幕权限
        val intent = Intent("com.remotecontrol.android.REQUEST_SCREEN_PERMISSION")
        sendBroadcast(intent)
        updateNotification("请授予屏幕录制权限")
    }
    
    private fun handleScreenPermissionGranted() {
        serviceScope.launch {
            try {
                Log.d(TAG, "Screen permission granted, starting screen capture")
                sendMessage(RemoteMessage(MessageType.SCREEN_RESPONSE, data = "granted"))
                updateNotification("已开始屏幕传输")
                
                if (hasMediaProjectionData()) {
                    screenCaptureManager.start(mediaProjectionResultCode, mediaProjectionData!!) { imageData ->
                        serviceScope.launch {
                            sendScreenData(imageData)
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error starting screen capture: ${e.message}")
                sendMessage(RemoteMessage(MessageType.SCREEN_RESPONSE, data = "error"))
            }
        }
    }
    
    private fun handleScreenPermissionDenied() {
        serviceScope.launch {
            Log.d(TAG, "Screen permission denied by user")
            sendMessage(RemoteMessage(MessageType.SCREEN_RESPONSE, data = "denied"))
            updateNotification("用户拒绝了屏幕录制权限")
        }
    }
    
    private fun startHeartbeat() {
        serviceScope.launch {
            while (isConnected.get()) {
                try {
                    delay(10000L) // 每10秒发送一次心跳（更频繁）
                    if (isConnected.get() && socket?.isConnected == true && !socket!!.isClosed) {
                        sendMessage(RemoteMessage(MessageType.HEARTBEAT))
                        Log.d(TAG, "Heartbeat sent")
                    } else {
                        Log.w(TAG, "Socket disconnected, stopping heartbeat")
                        break
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error sending heartbeat: ${e.message}")
                    // 不要立即break，给连接一次恢复机会
                    delay(5000L)
                    if (!isConnected.get() || socket?.isClosed == true) {
                        break
                    }
                }
            }
        }
    }
    
    private suspend fun handleTouchCommand(message: RemoteMessage) {
        try {
            val touchCommand = gson.fromJson(
                gson.toJson(message.data),
                TouchCommand::class.java
            )
            
            inputSimulator.performTouch(touchCommand)
        } catch (e: Exception) {
            Log.e(TAG, "Error handling touch command: ${e.message}")
        }
    }
    
    private suspend fun handleKeyCommand(message: RemoteMessage) {
        try {
            val keyCommand = gson.fromJson(
                gson.toJson(message.data),
                KeyCommand::class.java
            )
            
            inputSimulator.performKeyPress(keyCommand.keyCode)
        } catch (e: Exception) {
            Log.e(TAG, "Error handling key command: ${e.message}")
        }
    }
    
    private suspend fun handleTextCommand(message: RemoteMessage) {
        try {
            val textCommand = gson.fromJson(
                gson.toJson(message.data),
                TextCommand::class.java
            )
            
            inputSimulator.inputText(textCommand.text)
        } catch (e: Exception) {
            Log.e(TAG, "Error handling text command: ${e.message}")
        }
    }
    
    private suspend fun sendScreenData(imageData: ByteArray) {
        try {
            val base64Image = android.util.Base64.encodeToString(imageData, android.util.Base64.NO_WRAP)
            
            val screenData = ScreenData(
                imageBase64 = base64Image,
                width = screenCaptureManager.getScreenWidth(),
                height = screenCaptureManager.getScreenHeight(),
                quality = 80
            )
            
            val message = RemoteMessage(MessageType.SCREEN_DATA, data = screenData)
            sendMessage(message)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error sending screen data: ${e.message}")
        }
    }
    
    private suspend fun sendMessage(message: RemoteMessage) {
        try {
            if (!isConnected.get() || socket?.isClosed == true || socket?.isConnected == false) {
                Log.w(TAG, "Cannot send message: connection is closed")
                return
            }
            
            val json = gson.toJson(message)
            withContext(Dispatchers.IO) {
                outputStream?.write(json + "\n")
                outputStream?.flush()
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error sending message: ${e.message}")
            // 区分发送异常的严重程度
            when (e) {
                is java.net.SocketException, 
                is java.io.IOException -> {
                    Log.w(TAG, "Network error while sending message, connection may be lost")
                    handleUnexpectedDisconnection()
                }
                else -> {
                    Log.e(TAG, "Unexpected error sending message: ${e.javaClass.simpleName}")
                    // 对于其他异常，不立即断开连接
                }
            }
        }
    }
    
    private fun handleConnectionError(pcIP: String, pcPort: Int) {
        reconnectAttempts++
        
        if (reconnectAttempts <= maxReconnectAttempts) {
            Log.d(TAG, "Reconnection attempt $reconnectAttempts/$maxReconnectAttempts")
            updateNotification("连接失败，尝试重连... ($reconnectAttempts/$maxReconnectAttempts)")
            
            // 延迟后重试
            serviceScope.launch {
                delay(3000L * reconnectAttempts) // 递增延迟
                connectToPC(pcIP, pcPort)
            }
        } else {
            Log.e(TAG, "Max reconnection attempts reached")
            updateNotification("连接失败，请检查PC端是否启动")
            // 不要停止服务，让用户手动重试
            reconnectAttempts = 0  // 重置重试次数
        }
    }
    
    private fun handleUnexpectedDisconnection() {
        if (isConnected.get()) {
            Log.w(TAG, "Unexpected disconnection detected")
            updateNotification("连接断开，清理资源...")
            disconnectFromPC()
            
            // 添加自动重连逻辑（如果有存储的连接信息）
            // 注意：这里不自动重连，让用户手动重连以避免无限循环
            updateNotification("连接已断开，请手动重新连接")
        }
    }
    
    private fun updateNotification(text: String) {
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Android远程控制")
            .setContentText(text)
            .setSmallIcon(R.drawable.ic_notification)
            .setOngoing(true)
            .build()
        
        val notificationManager = getSystemService(NotificationManager::class.java)
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
}