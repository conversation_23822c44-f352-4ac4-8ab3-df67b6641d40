/*
 * Copyright 2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.plugins.ide.idea.model.internal;

import org.gradle.api.artifacts.component.ProjectComponentIdentifier;
import org.gradle.plugins.ide.idea.internal.IdeaModuleMetadata;
import org.gradle.plugins.ide.idea.model.ModuleDependency;
import org.gradle.plugins.ide.internal.IdeArtifactRegistry;

class ModuleDependencyBuilder {
    private final IdeArtifactRegistry ideArtifactRegistry;

    public ModuleDependencyBuilder(IdeArtifactRegistry ideArtifactRegistry) {
        this.ideArtifactRegistry = ideArtifactRegistry;
    }

    public ModuleDependency create(ProjectComponentIdentifier id, String scope) {
        return new ModuleDependency(determineProjectName(id), scope);
    }

    private String determineProjectName(ProjectComponentIdentifier id) {
        IdeaModuleMetadata moduleMetadata = ideArtifactRegistry.getIdeProject(IdeaModuleMetadata.class, id);
        return moduleMetadata == null ? id.getProjectName() : moduleMetadata.getName();
    }
}
