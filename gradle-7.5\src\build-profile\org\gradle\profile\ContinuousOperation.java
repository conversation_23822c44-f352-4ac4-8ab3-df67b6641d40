/*
 * Copyright 2011 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.gradle.profile;

/**
 * A continuous operation with a start and finish time.
 */
public class ContinuousOperation extends Operation {
    private long start;
    private long finish;
    private String description;

    public ContinuousOperation(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return description;
    }

    public ContinuousOperation setStart(long start) {
        this.start = start;
        return this;
    }

    public ContinuousOperation setFinish(long finish) {
        this.finish = finish;
        return this;
    }

    public long getStartTime() {
        return start;
    }

    @Override
    public long getElapsedTime() {
        return finish - start;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
