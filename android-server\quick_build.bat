@echo off
chcp 65001 >nul
title 快速APK编译

echo.
echo ==========================================
echo         Android APK 快速编译
echo ==========================================
echo.

:: 设置工作目录
cd /d "%~dp0"

:: 1. 环境检查
echo [1/6] 检查编译环境...

:: 检查Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ✗ Java环境未配置，请安装JDK 8+
    pause
    exit /b 1
)
echo ✓ Java环境正常

:: 检查Android SDK
if not defined ANDROID_SDK_ROOT if not defined ANDROID_HOME (
    echo ⚠ Android SDK环境变量未设置
    echo 尝试使用local.properties中的配置...
)

if exist local.properties (
    echo ✓ 找到local.properties配置
) else (
    echo ✗ 缺少local.properties文件
    echo 请确认Android SDK路径配置
    pause
    exit /b 1
)

:: 2. 检查项目文件
echo.
echo [2/6] 检查项目文件...

if not exist "build.gradle" (
    echo ✗ 缺少根目录build.gradle
    pause
    exit /b 1
)

if not exist "app\build.gradle" (
    echo ✗ 缺少app模块build.gradle
    pause
    exit /b 1
)

if not exist "app\src\main\AndroidManifest.xml" (
    echo ✗ 缺少AndroidManifest.xml
    pause
    exit /b 1
)

echo ✓ 项目文件完整

:: 3. 检查Gradle Wrapper
echo.
echo [3/6] 检查Gradle Wrapper...

if not exist "gradlew.bat" (
    echo ✗ 缺少gradlew.bat
    pause
    exit /b 1
)

if not exist "gradle\wrapper\gradle-wrapper.properties" (
    echo ✗ 缺少gradle-wrapper.properties
    pause
    exit /b 1
)

echo ✓ Gradle Wrapper配置完整

:: 4. 清理项目
echo.
echo [4/6] 清理项目...
echo 执行: gradlew.bat clean

call gradlew.bat clean
if %errorlevel% neq 0 (
    echo ✗ 项目清理失败，请检查错误信息
    echo.
    echo 常见问题解决:
    echo 1. 检查网络连接 (首次运行需要下载Gradle)
    echo 2. 检查防火墙设置
    echo 3. 尝试使用VPN或代理
    echo 4. 手动下载Gradle并解压到wrapper目录
    pause
    exit /b 1
)

echo ✓ 项目清理成功

:: 5. 编译APK
echo.
echo [5/6] 编译Debug APK...
echo 执行: gradlew.bat assembleDebug
echo.

call gradlew.bat assembleDebug
if %errorlevel% neq 0 (
    echo.
    echo ✗ APK编译失败
    echo.
    echo 请检查上述错误信息，常见解决方案:
    echo 1. 网络问题: 使用VPN或配置代理
    echo 2. SDK问题: 确认Android SDK路径正确
    echo 3. 依赖问题: 尝试 gradlew.bat --refresh-dependencies
    echo 4. Java版本: 确认使用JDK 8-17
    pause
    exit /b 1
)

echo.
echo ✓ APK编译成功!

:: 6. 验证输出
echo.
echo [6/6] 验证编译结果...

set APK_PATH=app\build\outputs\apk\debug\app-debug.apk
if exist "%APK_PATH%" (
    echo ✓ APK文件生成成功
    echo 文件路径: %APK_PATH%
    
    :: 获取文件大小
    for %%A in ("%APK_PATH%") do (
        set APK_SIZE=%%~zA
        set /a APK_SIZE_MB=%%~zA/1024/1024
    )
    
    echo 文件大小: %APK_SIZE% 字节 (~%APK_SIZE_MB% MB)
    
    :: 尝试获取APK信息 (如果有aapt工具)
    where aapt >nul 2>&1
    if %errorlevel% equ 0 (
        echo.
        echo APK信息:
        aapt dump badging "%APK_PATH%" 2>nul | findstr "package:\|versionCode\|versionName\|targetSdkVersion"
    )
    
) else (
    echo ✗ APK文件未找到
    echo 预期路径: %APK_PATH%
    echo 请检查编译日志中的错误信息
    pause
    exit /b 1
)

echo.
echo ==========================================
echo 编译完成!
echo ==========================================
echo.
echo 生成的APK文件:
echo %~dp0%APK_PATH%
echo.
echo 下一步操作:
echo 1. 运行 configure_apk.bat 配置并安装APK
echo 2. 或手动安装: adb install -r "%APK_PATH%"
echo 3. 或复制APK到Android设备手动安装
echo.

set /p RUN_CONFIG="是否立即运行配置脚本? (Y/n): "
if /i not "%RUN_CONFIG%"=="n" (
    if exist configure_apk.bat (
        echo.
        echo 启动配置脚本...
        call configure_apk.bat
    ) else (
        echo ⚠ configure_apk.bat 脚本不存在
    )
) else (
    echo.
    echo 编译完成，您可以稍后手动运行配置脚本
)

echo.
echo 按任意键退出...
pause >nul