// Generated by view binder compiler. Do not edit!
package com.remotecontrol.android.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.remotecontrol.android.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final Button btnAccessibilityPermission;

  @NonNull
  public final Button btnMediaProjectionPermission;

  @NonNull
  public final Button btnStartService;

  @NonNull
  public final Button btnStopService;

  @NonNull
  public final Button btnSystemAlertPermission;

  @NonNull
  public final TextView txtConnectedClients;

  @NonNull
  public final TextView txtIpAddress;

  @NonNull
  public final TextView txtPort;

  @NonNull
  public final TextView txtServiceStatus;

  private ActivityMainBinding(@NonNull ScrollView rootView,
      @NonNull Button btnAccessibilityPermission, @NonNull Button btnMediaProjectionPermission,
      @NonNull Button btnStartService, @NonNull Button btnStopService,
      @NonNull Button btnSystemAlertPermission, @NonNull TextView txtConnectedClients,
      @NonNull TextView txtIpAddress, @NonNull TextView txtPort,
      @NonNull TextView txtServiceStatus) {
    this.rootView = rootView;
    this.btnAccessibilityPermission = btnAccessibilityPermission;
    this.btnMediaProjectionPermission = btnMediaProjectionPermission;
    this.btnStartService = btnStartService;
    this.btnStopService = btnStopService;
    this.btnSystemAlertPermission = btnSystemAlertPermission;
    this.txtConnectedClients = txtConnectedClients;
    this.txtIpAddress = txtIpAddress;
    this.txtPort = txtPort;
    this.txtServiceStatus = txtServiceStatus;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAccessibilityPermission;
      Button btnAccessibilityPermission = ViewBindings.findChildViewById(rootView, id);
      if (btnAccessibilityPermission == null) {
        break missingId;
      }

      id = R.id.btnMediaProjectionPermission;
      Button btnMediaProjectionPermission = ViewBindings.findChildViewById(rootView, id);
      if (btnMediaProjectionPermission == null) {
        break missingId;
      }

      id = R.id.btnStartService;
      Button btnStartService = ViewBindings.findChildViewById(rootView, id);
      if (btnStartService == null) {
        break missingId;
      }

      id = R.id.btnStopService;
      Button btnStopService = ViewBindings.findChildViewById(rootView, id);
      if (btnStopService == null) {
        break missingId;
      }

      id = R.id.btnSystemAlertPermission;
      Button btnSystemAlertPermission = ViewBindings.findChildViewById(rootView, id);
      if (btnSystemAlertPermission == null) {
        break missingId;
      }

      id = R.id.txtConnectedClients;
      TextView txtConnectedClients = ViewBindings.findChildViewById(rootView, id);
      if (txtConnectedClients == null) {
        break missingId;
      }

      id = R.id.txtIpAddress;
      TextView txtIpAddress = ViewBindings.findChildViewById(rootView, id);
      if (txtIpAddress == null) {
        break missingId;
      }

      id = R.id.txtPort;
      TextView txtPort = ViewBindings.findChildViewById(rootView, id);
      if (txtPort == null) {
        break missingId;
      }

      id = R.id.txtServiceStatus;
      TextView txtServiceStatus = ViewBindings.findChildViewById(rootView, id);
      if (txtServiceStatus == null) {
        break missingId;
      }

      return new ActivityMainBinding((ScrollView) rootView, btnAccessibilityPermission,
          btnMediaProjectionPermission, btnStartService, btnStopService, btnSystemAlertPermission,
          txtConnectedClients, txtIpAddress, txtPort, txtServiceStatus);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
