/*
 * Copyright 2018 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.gradle.buildinit.plugins.internal;

import javax.annotation.Nullable;

public interface DependenciesBuilder {
    void projectDependency(String configuration, @Nullable String comment, String projectPath);

    void platformDependency(String configuration, @Nullable String comment, String dependency);

    void selfDependency(String configuration, @Nullable String comment);

    void dependency(String configuration, @Nullable String comment, String... dependencies);

    void dependencyConstraint(String configuration, @Nullable String comment, String... dependencies);
}
