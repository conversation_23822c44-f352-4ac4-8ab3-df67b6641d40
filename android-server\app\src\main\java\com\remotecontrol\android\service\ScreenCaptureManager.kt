package com.remotecontrol.android.service

import android.content.Context
import android.content.Intent
import android.graphics.*
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Handler
import android.os.HandlerThread
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import java.io.ByteArrayOutputStream
import java.nio.ByteBuffer

class ScreenCaptureManager(private val context: Context) {
    
    companion object {
        private const val TAG = "ScreenCaptureManager"
        private const val VIRTUAL_DISPLAY_NAME = "RemoteControlScreenCapture"
    }
    
    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var imageReader: ImageReader? = null
    private var backgroundThread: HandlerThread? = null
    private var backgroundHandler: Handler? = null
    
    private var screenWidth = 0
    private var screenHeight = 0
    private var screenDensity = 0
    private var isCapturing = false
    
    private var onScreenCaptured: ((ByteArray) -> Unit)? = null
    
    init {
        initScreenMetrics()
    }
    
    private fun initScreenMetrics() {
        val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        
        screenWidth = displayMetrics.widthPixels
        screenHeight = displayMetrics.heightPixels
        screenDensity = displayMetrics.densityDpi
        
        Log.d(TAG, "Screen metrics: ${screenWidth}x${screenHeight}, density: $screenDensity")
    }
    
    fun getScreenWidth(): Int = screenWidth
    fun getScreenHeight(): Int = screenHeight
    
    fun start(resultCode: Int, data: Intent, callback: (ByteArray) -> Unit): Boolean {
        if (isCapturing) {
            Log.w(TAG, "Screen capture already running")
            return true
        }
        
        onScreenCaptured = callback
        
        try {
            // 创建后台线程
            backgroundThread = HandlerThread("ScreenCaptureThread").apply {
                start()
            }
            backgroundHandler = Handler(backgroundThread!!.looper)
            
            // 获取MediaProjection
            val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data)
            
            if (mediaProjection == null) {
                Log.e(TAG, "Failed to create MediaProjection")
                return false
            }
            
            // 创建ImageReader
            imageReader = ImageReader.newInstance(
                screenWidth, screenHeight,
                PixelFormat.RGBA_8888, 2
            )
            
            imageReader!!.setOnImageAvailableListener({ reader ->
                val image = reader?.acquireLatestImage()
                image?.let {
                    processImage(it)
                    it.close()
                }
            }, backgroundHandler)
            
            // 创建VirtualDisplay
            virtualDisplay = mediaProjection!!.createVirtualDisplay(
                VIRTUAL_DISPLAY_NAME,
                screenWidth, screenHeight, screenDensity,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader!!.surface,
                null, null
            )
            
            isCapturing = true
            Log.d(TAG, "Screen capture started successfully")
            return true
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start screen capture: ${e.message}")
            stop()
            return false
        }
    }
    
    private fun processImage(image: Image) {
        try {
            val planes = image.planes
            val buffer = planes[0].buffer
            val pixelStride = planes[0].pixelStride
            val rowStride = planes[0].rowStride
            val rowPadding = rowStride - pixelStride * screenWidth
            
            // 创建Bitmap
            val bitmap = Bitmap.createBitmap(
                screenWidth + rowPadding / pixelStride,
                screenHeight,
                Bitmap.Config.ARGB_8888
            )
            
            bitmap.copyPixelsFromBuffer(buffer)
            
            // 如果有padding，需要裁剪
            val finalBitmap = if (rowPadding != 0) {
                Bitmap.createBitmap(bitmap, 0, 0, screenWidth, screenHeight)
            } else {
                bitmap
            }
            
            // 压缩为JPEG
            val outputStream = ByteArrayOutputStream()
            val compressed = finalBitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream)
            
            if (compressed) {
                val imageData = outputStream.toByteArray()
                onScreenCaptured?.invoke(imageData)
            }
            
            // 清理资源
            outputStream.close()
            if (finalBitmap != bitmap) {
                bitmap.recycle()
            }
            finalBitmap.recycle()
            
        } catch (e: Exception) {
            Log.e(TAG, "Error processing image: ${e.message}")
        }
    }
    
    fun stop() {
        isCapturing = false
        
        try {
            virtualDisplay?.release()
            virtualDisplay = null
            
            imageReader?.close()
            imageReader = null
            
            mediaProjection?.stop()
            mediaProjection = null
            
            backgroundThread?.quitSafely()
            try {
                backgroundThread?.join(1000)
            } catch (e: InterruptedException) {
                // 忽略中断异常
            }
            backgroundThread = null
            backgroundHandler = null
            
            Log.d(TAG, "Screen capture stopped")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping screen capture: ${e.message}")
        }
    }
    
    /**
     * 设置屏幕截图质量
     */
    fun setQuality(quality: Int) {
        // 这里可以根据质量调整压缩参数
        // 当前实现使用固定的80%质量
    }
    
    /**
     * 设置屏幕截图缩放
     */
    fun setScale(scale: Float) {
        if (scale > 0 && scale <= 1.0f) {
            // 重新计算屏幕尺寸
            val newWidth = (screenWidth * scale).toInt()
            val newHeight = (screenHeight * scale).toInt()
            
            // 如果正在捕获，需要重新启动
            if (isCapturing) {
                Log.d(TAG, "Restarting capture with new scale: $scale")
                // 这里可以实现缩放重启逻辑
            }
        }
    }
}