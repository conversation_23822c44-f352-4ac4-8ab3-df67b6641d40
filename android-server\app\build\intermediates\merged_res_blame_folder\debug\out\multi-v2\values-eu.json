{"logs": [{"outputFile": "com.remotecontrol.android.app-mergeDebugResources-36:/values-eu/values-eu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c5bc1c461f48fbd1a3b03cc22fccf5ce\\transformed\\appcompat-1.6.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,312,422,508,614,738,824,905,997,1091,1187,1281,1382,1476,1572,1669,1761,1854,1936,2045,2154,2253,2362,2469,2580,2751,2850", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "209,307,417,503,609,733,819,900,992,1086,1182,1276,1377,1471,1567,1664,1756,1849,1931,2040,2149,2248,2357,2464,2575,2746,2845,2928"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "325,434,532,642,728,834,958,1044,1125,1217,1311,1407,1501,1602,1696,1792,1889,1981,2074,2156,2265,2374,2473,2582,2689,2800,2971,9372", "endColumns": "108,97,109,85,105,123,85,80,91,93,95,93,100,93,95,96,91,92,81,108,108,98,108,106,110,170,98,82", "endOffsets": "429,527,637,723,829,953,1039,1120,1212,1306,1402,1496,1597,1691,1787,1884,1976,2069,2151,2260,2369,2468,2577,2684,2795,2966,3065,9450"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c6dc7adb4bd25c9f6fecda703177518b\\transformed\\core-1.12.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3569,3667,3770,3870,3973,4078,4181,9455", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "3662,3765,3865,3968,4073,4176,4295,9551"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10dc035393e004282fd64c0079b394e4\\transformed\\material-1.11.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,275,382,487,567,674,774,872,987,1070,1137,1236,1304,1365,1453,1516,1582,1646,1717,1780,1834,1943,2002,2065,2119,2193,2318,2408,2488,2633,2716,2798,2936,3027,3110,3162,3215,3281,3352,3432,3518,3598,3676,3754,3827,3902,4009,4096,4183,4274,4367,4439,4515,4607,4658,4740,4806,4890,4976,5038,5102,5165,5233,5340,5449,5545,5650,5706,5763", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,137,90,82,51,52,65,70,79,85,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82", "endOffsets": "270,377,482,562,669,769,867,982,1065,1132,1231,1299,1360,1448,1511,1577,1641,1712,1775,1829,1938,1997,2060,2114,2188,2313,2403,2483,2628,2711,2793,2931,3022,3105,3157,3210,3276,3347,3427,3513,3593,3671,3749,3822,3897,4004,4091,4178,4269,4362,4434,4510,4602,4653,4735,4801,4885,4971,5033,5097,5160,5228,5335,5444,5540,5645,5701,5758,5841"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3177,3282,3362,3469,4300,4398,4513,4596,4663,4762,4830,4891,4979,5042,5108,5172,5243,5306,5360,5469,5528,5591,5645,5719,5844,5934,6014,6159,6242,6324,6462,6553,6636,6688,6741,6807,6878,6958,7044,7124,7202,7280,7353,7428,7535,7622,7709,7800,7893,7965,8041,8133,8184,8266,8332,8416,8502,8564,8628,8691,8759,8866,8975,9071,9176,9232,9289", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,106,104,79,106,99,97,114,82,66,98,67,60,87,62,65,63,70,62,53,108,58,62,53,73,124,89,79,144,82,81,137,90,82,51,52,65,70,79,85,79,77,77,72,74,106,86,86,90,92,71,75,91,50,81,65,83,85,61,63,62,67,106,108,95,104,55,56,82", "endOffsets": "320,3172,3277,3357,3464,3564,4393,4508,4591,4658,4757,4825,4886,4974,5037,5103,5167,5238,5301,5355,5464,5523,5586,5640,5714,5839,5929,6009,6154,6237,6319,6457,6548,6631,6683,6736,6802,6873,6953,7039,7119,7197,7275,7348,7423,7530,7617,7704,7795,7888,7960,8036,8128,8179,8261,8327,8411,8497,8559,8623,8686,8754,8861,8970,9066,9171,9227,9284,9367"}}]}]}